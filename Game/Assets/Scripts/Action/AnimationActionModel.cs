using System.Collections.Generic;
using UnityEngine;

public class AnimationActionModel : ActionModel
{
    public AnimationActionAsset ActionAsset;

    public bool Force => ActionAsset.Force;
    public bool MovementDisabling
    {
        get
        {
            if (MakeInteruptable)
            {
                return false;
            }

            return ActionAsset.MovementDisabling;
        }
    }
    public bool Invulnerable => ActionAsset.Invulnerable || tempInvulnerable;
    public bool Interuptable => ActionAsset.Interuptable || MakeInteruptable;
    public bool Blocking => ActionAsset.Blocking;
    public bool ForceInteruptable => ActionAsset.ForceInteruptable;
    public bool SoftStartInteruptable => ActionAsset.SoftStartInteruptable;
    public bool SoftStartInterrupter => ActionAsset.SoftStartInterrupter;
    public bool SoftFinishActionInterrupter => ActionAsset.SoftFinishActionInterrupter || SoftFinishActionInterrupterOverride;
    public bool DeathAction => ActionAsset.DeathAction;
    public float AcceptInputDelay => ActionAsset.AcceptInputDelay;
    public List<AnimationEventComponent> animationEvents => ActionAsset.AnimationEvents;

    // Animation can be overriden by movement
    public bool SoftFinish = false;

    // Animation can be override by a defensive action.
    public bool SoftFinishAction = false;
    public bool SoftStart = false;
    public bool Combo = false;
    public bool ComboInterrupter = false;
    public bool AcceptInput = false;
    public AnimationActionModel NextAction;
    public bool MakeInteruptable = false;
    public bool SoftFinishActionInterrupterOverride = false;
    public bool UseEarlyStartPercent = false;
    public float MixSpeedModifier = 1f;
    public bool Power = false;

    private bool tempInvulnerable = false;

    public Combatant Target;

    public bool CanSetNextAction => AcceptInput;

    public AnimationActionModel(AnimationActionAsset actionAsset)
    {
        ActionAsset = actionAsset;
    }

    public AnimationModel Play(Combatant combatant)
    {
        if (ActionAsset.LookAtTargetStrategy != null)
        {
            ActionAsset.LookAtTargetStrategy.LookForTargetAsync(combatant, target =>
            {
                if (target != null)
                {
                    var direction = VectorHelpers.GetDirectionXZ(combatant.transform, target.transform);
                    combatant.MovementBehavior.FastRotate(direction);
                }
                else
                {
                    HandleFastRotate(combatant);
                }
            });
        }
        else
        {
            HandleFastRotate(combatant);
        }

        var animation = ActionAsset.Animation;
        var model = animation.GenerateModel();
        model.UseEarlyStartPercent = UseEarlyStartPercent;
        model.MixSpeedModifier = MixSpeedModifier;
        combatant.AnimationManager.Play(model);
        return model;
    }

    private void HandleFastRotate(Combatant combatant)
    {
        if (ActionAsset.RotateOnStart)
        {
            combatant.MovementBehavior.FastRotate();
        }
    }

    public void OnAnimationEvent(string eventName, Combatant combatant)
    {
        var list = ActionAsset.AnimationEvents;
        for (int i = 0, n = list.Count; i < n; i++)
        {
            var ev = list[i];
            if (ev.EventName == eventName)
                ev.OnEvent(combatant, this);
        }

        if (eventName == Constants.InvulnerableStartEvent)
            tempInvulnerable = true;
        else if (eventName == Constants.InvulnerableEndEvent)
            tempInvulnerable = false;
    }

    public override bool Execute(Combatant combatant)
    {
        return Play(combatant) != null;
    }

    public void Reset(AnimationActionAsset asset)
    {
        ActionAsset = asset;
        NextAction = null;
        MakeInteruptable = false;
        SoftFinish = false;
        SoftStart = false;
        Combo = false;
        ComboInterrupter = false;
        AcceptInput = false;
        SoftFinishActionInterrupterOverride = false;
        UseEarlyStartPercent = false;
        MixSpeedModifier = 1f;
        Power = false;
        Target = null;
        tempInvulnerable = false;
    }
}
