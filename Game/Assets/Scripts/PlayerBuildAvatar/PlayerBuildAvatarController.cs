using ECM2;
using UniRx;
using UnityEngine;

public class PlayerBuildAvatarController : BaseMonoBehavior
{
    [HideInInspector]
    public PlayerControlsManager Controls;

    public Transform CameraTarget;
    private Transform CameraTransform;
    private Character Character;
    private AvatarBuildBehavior AvatarBuildBehavior;
    
    private float lookSensitivity = 1f;
    private float pitchMin = -89f;
    private float pitchMax = 89f;
    private float pitch;
    private float yaw; 
    
    public Vector3 MovementDirection
    {
        get
        {
            var input = Controls.InputVector;

            if (input.sqrMagnitude < 0.01f)
                return Vector3.zero;

            var cameraForward = CameraTransform.forward;
            var cameraRight = CameraTransform.right;
            var move = (cameraRight * input.x + cameraForward * input.y);

            return move.normalized;
        }
    }
    
    void Awake()
    {
        if (Controls == null)
        {
            Controls = ControllerInputManager.Instance.Player1ControlsManager;
        }
    }
    
    void Start()
    {
        AvatarBuildBehavior = GetComponent<AvatarBuildBehavior>();
        PlayerCameraManager.Instance.SetPlayerBuildAvatar(CameraTarget);
        CameraTransform = PlayerCameraManager.Instance.MainCamera.transform;
        Character = GetComponent<Character>();
        
        AddDisposable(Controls.OnInputJump.Subscribe(o =>
        {
            AvatarBuildBehavior.TryBuild();
        }, Debug.LogError));
        
        AddDisposable(Controls.OnInputRotateLeft.Subscribe(o =>
        {
            AvatarBuildBehavior.RotateLeft();
        }, Debug.LogError));
        
        AddDisposable(Controls.OnInputRotateRight.Subscribe(o =>
        {
            AvatarBuildBehavior.RotateRight();
        }, Debug.LogError));
    }

    void Update()
    {
        HandleLook();
        HandleMovement();
    }
    
    private void HandleLook()
    {
        var lookDelta = Controls.LookVector * lookSensitivity;
        yaw += lookDelta.x;
        pitch -= lookDelta.y;
        pitch = Mathf.Clamp(pitch, pitchMin, pitchMax);
        CameraTarget.rotation = Quaternion.Euler(pitch, yaw, 0f);
    }

    private void HandleMovement()
    {
        Character.SetMovementDirection(MovementDirection);
    }

    protected override void OnDestroy()
    {
        if (Toolbox.Instance != null && PlayerCameraManager.Instance != null)
        {
            PlayerCameraManager.Instance.ClearPlayerBuildAvatar();
        }
        base.OnDestroy();
    }
}
