using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class WeaponFX : MonoBehaviour
{
	public GameObject spawnPoint;
	public GameObject effectToSpawn;

	public GameObject HitFXSpawnPoint;
	public GameObject HitFX;

	public void SpawnSwipeFX()
	{
		var vfx = Instantiate(effectToSpawn, spawnPoint.transform.position, spawnPoint.transform.rotation);
		vfx.transform.SetParent(transform);

		var ps = vfx.GetComponent<ParticleSystem>();
		if (ps != null)
		{
			Destroy(vfx, ps.main.duration + ps.main.startLifetime.constantMax);
		}
		else
		{
			var psChild = vfx.transform.GetChild(0).GetComponent<ParticleSystem>();
			if (psChild != null)
				Destroy(vfx, psChild.main.duration + psChild.main.startLifetime.constantMax);
		}
	}

	public void SpawnHitFX(Vector3 collisionPoint)
    {
		if (HitFX == null || HitFXSpawnPoint == null)
        {
			return;
        }
		ParticleObjectPool.Instance.Play(HitFX, collisionPoint);
    }
}
