using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;

[CreateAssetMenu(menuName = "Item/Health")]
public class HealthItem : ItemObject
{
    [HideInInspector]
    public float HealAmount = 0f;
    public float HealPercent = 0.5f;

    [HideInInspector]
    public float CurrentAmount;

    public bool Empty()
    {
        return CurrentAmount <= 0;
    }

    public float Consume(float amount)
    {
        var newAmount = CurrentAmount - amount;

        if (newAmount <= 0)
        {
            newAmount = CurrentAmount;
            CurrentAmount = 0;
            return newAmount;
        }

        CurrentAmount = newAmount;

        return amount;
    }

    public void Reset()
    {
        CurrentAmount = HealAmount;
    }

    public void SetPercentHealAmount(float totalHealth)
    {
        if (HealPercent == 0f)
        {
            return;
        }

        HealAmount = totalHealth * HealPercent;
        CurrentAmount = HealAmount;
    }

    private void OnEnable()
    {
        Reset();
    }
}
