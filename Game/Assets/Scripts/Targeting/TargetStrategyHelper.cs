using System.Collections.Generic;
using UnityEngine;

public static class TargetStrategyHelper
{
    private static readonly Collider[] colliderHits = new Collider[1024];
    private static readonly List<GameObject> reusableTargets = new(64);
    private static readonly List<GameObject> front = new(64);
    private static readonly List<GameObject> side = new(64);
    private static readonly List<GameObject> back = new(64);

    public static GameObject FindClosestTargetDirectional(
        GameObject origin,
        Vector3 position,
        float radius,
        int layerMask,
        Allegiance allegiance,
        Vector3 movementDirection = default)
    {
        int hitCount = Physics.OverlapSphereNonAlloc(position, radius, colliderHits, layerMask);
        reusableTargets.Clear();

        for (int i = 0; i < hitCount; i++)
        {
            var hit = colliderHits[i];
            if (hit == null || hit.gameObject == origin) continue;

            if (hit.TryGetComponent<Combatant>(out var combatant) && combatant.Allegiance == allegiance)
            {
                reusableTargets.Add(hit.gameObject);
            }
        }

        return GetClosestByDirection(origin, position, reusableTargets, movementDirection);
    }

    public static GameObject FindClosestTarget(
        GameObject origin,
        Vector3 position,
        float radius,
        int layerMask,
        Allegiance allegiance)
    {
        int hitCount = Physics.OverlapSphereNonAlloc(position, radius, colliderHits, layerMask);
        GameObject closest = null;
        float closestDistanceSqr = float.MaxValue;

        for (int i = 0; i < hitCount; i++)
        {
            var hit = colliderHits[i];
            if (hit == null || hit.gameObject == origin) continue;

            if (hit.TryGetComponent<Combatant>(out var combatant) && combatant.Allegiance == allegiance)
            {
                float distSqr = (hit.transform.position - position).sqrMagnitude;
                if (distSqr < closestDistanceSqr)
                {
                    closestDistanceSqr = distSqr;
                    closest = hit.gameObject;
                }
            }
        }

        return closest;
    }

    private static GameObject GetClosestByDirection(
        GameObject origin,
        Vector3 originPosition,
        List<GameObject> targets,
        Vector3 movementDirection)
    {
        front.Clear();
        side.Clear();
        back.Clear();

        Vector3 direction = movementDirection != Vector3.zero ? movementDirection.normalized : origin.transform.forward;

        for (int i = 0; i < targets.Count; i++)
        {
            GameObject target = targets[i];
            Vector3 toTarget = (target.transform.position - origin.transform.position).normalized;
            float dot = Vector3.Dot(direction, toTarget);

            if (dot > 0.3f) front.Add(target);
            else if (dot < -0.3f) back.Add(target);
            else side.Add(target);
        }

        if (front.Count > 0)
            return GetClosest(originPosition, front);
        else if (side.Count > 0)
            return GetClosest(originPosition, side);
        else if (back.Count > 0 && movementDirection.IsLowInput())
            return GetClosest(originPosition, back);

        return null;
    }

    private static GameObject GetClosest(Vector3 origin, List<GameObject> candidates)
    {
        GameObject closest = null;
        float closestDistSqr = float.MaxValue;

        for (int i = 0; i < candidates.Count; i++)
        {
            var target = candidates[i];
            float distSqr = (target.transform.position - origin).sqrMagnitude;
            if (distSqr < closestDistSqr)
            {
                closestDistSqr = distSqr;
                closest = target;
            }
        }

        return closest;
    }
}
