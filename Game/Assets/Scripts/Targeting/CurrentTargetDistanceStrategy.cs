using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(menuName = "Targeting/CurrentTargetDistanceStrategy")]
public class CurrentTargetDistanceStrategy : TargetStrategy
{
    public float MaxDistance = 0f;
    public float MinDistance = 0f;

    public override Combatant LookForTarget(Combatant combatant)
    {
        var target = combatant.TargetProvider.CurrentTarget;
        if (target == null)
        {
            return null;
        }

        var distance = Vector3.Distance(combatant.transform.position, target.transform.position);
        if (distance >= MinDistance && distance <= MaxDistance)
        {
            return target;
        }

        return null;
    }

    public override void LookForTargetAsync(Combatant combatant, Action<Combatant> callback)
    {
        callback.Invoke(LookForTarget(combatant));
    }
}