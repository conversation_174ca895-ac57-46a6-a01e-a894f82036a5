using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ParticleObjectPool : BaseMonoBehavior
{
    public static ParticleObjectPool Instance
    {
        get
        {
            return Toolbox.Instance.Get<ParticleObjectPool>();
        }
    }

    private Dictionary<GameObject, ParticleSystem> Particles;

    private void Awake()
    {
        Particles = new Dictionary<GameObject, ParticleSystem>();
    }

    public void Play(GameObject particle, Vector3 position)
    {
        if (Particles == null)
        {
            return;
        }

        if (Particles.ContainsKey(particle))
        {
            var instance = Particles[particle];
            if (instance != null)
            {
                instance.transform.position = position;
                instance.Play(true);
                return;
            }
        }

        var effect = Instantiate(particle, position, Quaternion.identity);
        effect.transform.parent = transform;
        var particleSystem = effect.GetComponent<ParticleSystem>();
        var main = particleSystem.main;
        main.playOnAwake = false;
        main.simulationSpace = ParticleSystemSimulationSpace.World;
        var children = effect.GetComponentsInChildren<ParticleSystem>();
        foreach (var child in children)
        {
            var childMain = child.main;
            childMain.playOnAwake = false;
            childMain.simulationSpace = ParticleSystemSimulationSpace.World;
        }
        particleSystem.Play(true);
        Particles[particle] = particleSystem;
    }

}
