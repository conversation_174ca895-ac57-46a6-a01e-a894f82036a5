using System;
using System.Collections.Generic;
using Unity.Collections;
using UnityEngine;

public class PhysicsCommandManager : BaseMonoBehavior
{
    public static PhysicsCommandManager Instance => Toolbox.Instance.Get<PhysicsCommandManager>();
    private const int MaxHits = 512;

    private readonly List<SpherecastCommandRequest> spherecastRequests = new();
    private readonly List<RaycastCommandRequest> raycastRequests = new();
    private readonly List<BoxcastCommandRequest> boxcastRequests = new();
    private readonly List<OverlapSphereCommandRequest> overlapSphereRequests = new();
    private readonly List<OverlapBoxCommandRequest> overlapBoxRequests = new();
    
    private static readonly ColliderHit[] sharedSphereHitBuffer = new ColliderHit[MaxHits];
    private static readonly ColliderHit[] sharedBoxHitBuffer = new ColliderHit[MaxHits];
    
    private int frameSkipRate = 0;
    private int frameCounter = 0;

    private void LateUpdate()
    {
        frameCounter++;
        if (frameCounter < frameSkipRate)
            return;

        frameCounter = 0;
        
        ScheduleSpherecastBatch();
        ScheduleRaycastBatch();
        ScheduleBoxcastBatch();
        ScheduleOverlapSphereBatch();
        ScheduleOverlapBoxBatch();
    }

    public void RequestSpherecast(SpherecastCommand command, Action<RaycastHit> callback)
    {
        spherecastRequests.Add(new SpherecastCommandRequest(command, callback));
    }

    public void RequestRaycast(RaycastCommand command, Action<RaycastHit> callback)
    {
        raycastRequests.Add(new RaycastCommandRequest(command, callback));
    }

    public void RequestBoxcast(BoxcastCommand command, Action<RaycastHit> callback)
    {
        boxcastRequests.Add(new BoxcastCommandRequest(command, callback));
    }

    public void RequestOverlapSphere(OverlapSphereCommand command, Action<ColliderHit[]> callback)
    {
        overlapSphereRequests.Add(new OverlapSphereCommandRequest(command, callback));
    }

    public void RequestOverlapBox(OverlapBoxCommand command, Action<ColliderHit[]> callback)
    {
        overlapBoxRequests.Add(new OverlapBoxCommandRequest(command, callback));
    }

    private void ScheduleSpherecastBatch()
    {
        if (spherecastRequests.Count == 0) return;

        var commands = new NativeArray<SpherecastCommand>(spherecastRequests.Count, Allocator.TempJob);
        var results = new NativeArray<RaycastHit>(spherecastRequests.Count, Allocator.TempJob);

        for (int i = 0; i < spherecastRequests.Count; i++)
            commands[i] = spherecastRequests[i].Command;

        var handle = SpherecastCommand.ScheduleBatch(commands, results, 1);
        handle.Complete();

        for (int i = 0; i < spherecastRequests.Count; i++)
            spherecastRequests[i].Callback?.Invoke(results[i]);

        commands.Dispose();
        results.Dispose();
        spherecastRequests.Clear();
    }

    private void ScheduleRaycastBatch()
    {
        if (raycastRequests.Count == 0) return;

        var commands = new NativeArray<RaycastCommand>(raycastRequests.Count, Allocator.TempJob);
        var results = new NativeArray<RaycastHit>(raycastRequests.Count, Allocator.TempJob);

        for (int i = 0; i < raycastRequests.Count; i++)
            commands[i] = raycastRequests[i].Command;

        var handle = RaycastCommand.ScheduleBatch(commands, results, 1);
        handle.Complete();

        for (int i = 0; i < raycastRequests.Count; i++)
            raycastRequests[i].Callback?.Invoke(results[i]);

        commands.Dispose();
        results.Dispose();
        raycastRequests.Clear();
    }

    private void ScheduleBoxcastBatch()
    {
        if (boxcastRequests.Count == 0) return;

        var commands = new NativeArray<BoxcastCommand>(boxcastRequests.Count, Allocator.TempJob);
        var results = new NativeArray<RaycastHit>(boxcastRequests.Count, Allocator.TempJob);

        for (int i = 0; i < boxcastRequests.Count; i++)
            commands[i] = boxcastRequests[i].Command;

        var handle = BoxcastCommand.ScheduleBatch(commands, results, 1);
        handle.Complete();

        for (int i = 0; i < boxcastRequests.Count; i++)
            boxcastRequests[i].Callback?.Invoke(results[i]);

        commands.Dispose();
        results.Dispose();
        boxcastRequests.Clear();
    }

    private void ScheduleOverlapSphereBatch()
    {
        int count = overlapSphereRequests.Count;
        if (count == 0) return;
        
        int totalHits = count * MaxHits;
        var commands = new NativeArray<OverlapSphereCommand>(count, Allocator.TempJob);
        var results = new NativeArray<ColliderHit>(totalHits, Allocator.TempJob);

        for (int i = 0; i < count; i++)
            commands[i] = overlapSphereRequests[i].Command;

        var handle = OverlapSphereCommand.ScheduleBatch(commands, results, 1, MaxHits);
        handle.Complete();

        for (int i = 0; i < count; i++)
        {
            var srcIndex = i * MaxHits;
            if (srcIndex + MaxHits > results.Length)
            {
                Debug.LogError($"srcIndex {srcIndex} + MaxHits {MaxHits} > results.Length {results.Length}");
                continue;
            }

            NativeArray<ColliderHit>.Copy(results, srcIndex, sharedSphereHitBuffer, 0, MaxHits);
            overlapSphereRequests[i].Callback?.Invoke(sharedSphereHitBuffer);
        }

        commands.Dispose();
        results.Dispose();
        overlapSphereRequests.Clear();
    }

    private void ScheduleOverlapBoxBatch()
    {
        int count = overlapBoxRequests.Count;
        if (count == 0) return;

        int totalHits = count * MaxHits;
        var commands = new NativeArray<OverlapBoxCommand>(count, Allocator.TempJob);
        var results = new NativeArray<ColliderHit>(totalHits, Allocator.TempJob);

        for (int i = 0; i < count; i++)
            commands[i] = overlapBoxRequests[i].Command;

        var handle = OverlapBoxCommand.ScheduleBatch(commands, results, 1, MaxHits);
        handle.Complete();

        for (int i = 0; i < count; i++)
        {
            var srcIndex = i * MaxHits;
            if (srcIndex + MaxHits > results.Length)
            {
                Debug.LogError($"srcIndex {srcIndex} + MaxHits {MaxHits} > results.Length {results.Length}");
                continue;
            }

            NativeArray<ColliderHit>.Copy(results, srcIndex, sharedBoxHitBuffer, 0, MaxHits);
            overlapBoxRequests[i].Callback?.Invoke(sharedBoxHitBuffer);
        }

        commands.Dispose();
        results.Dispose();
        overlapBoxRequests.Clear();
    }

    public struct SpherecastCommandRequest
    {
        public SpherecastCommand Command;
        public Action<RaycastHit> Callback;
        public SpherecastCommandRequest(SpherecastCommand command, Action<RaycastHit> callback)
        {
            Command = command;
            Callback = callback;
        }
    }

    public struct RaycastCommandRequest
    {
        public RaycastCommand Command;
        public Action<RaycastHit> Callback;
        public RaycastCommandRequest(RaycastCommand command, Action<RaycastHit> callback)
        {
            Command = command;
            Callback = callback;
        }
    }

    public struct BoxcastCommandRequest
    {
        public BoxcastCommand Command;
        public Action<RaycastHit> Callback;
        public BoxcastCommandRequest(BoxcastCommand command, Action<RaycastHit> callback)
        {
            Command = command;
            Callback = callback;
        }
    }

    public struct OverlapSphereCommandRequest
    {
        public OverlapSphereCommand Command;
        public Action<ColliderHit[]> Callback;
        public OverlapSphereCommandRequest(OverlapSphereCommand command, Action<ColliderHit[]> callback)
        {
            Command = command;
            Callback = callback;
        }
    }

    public struct OverlapBoxCommandRequest
    {
        public OverlapBoxCommand Command;
        public Action<ColliderHit[]> Callback;
        public OverlapBoxCommandRequest(OverlapBoxCommand command, Action<ColliderHit[]> callback)
        {
            Command = command;
            Callback = callback;
        }
    }
}
