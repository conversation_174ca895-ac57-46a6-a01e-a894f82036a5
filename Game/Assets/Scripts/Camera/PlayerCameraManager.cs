using UnityEngine;
using Unity.Cinemachine;
using UnityEngine.Serialization;

public class PlayerCameraManager : BaseMonoBehavior
{
    public static PlayerCameraManager Instance => Toolbox.Instance.Get<PlayerCameraManager>();

    public Camera MainCamera;
    public CinemachineCamera PlayerCamera;
    public CinemachineCamera PlayerBuildCamera;
    
    private Transform playerTarget;
    
    public void SetPlayerCameraTarget(Transform playerCameraTarget)
    {
        if (PlayerCamera == null)
        {
            return;
        }
        PlayerCamera.Target = new Unity.Cinemachine.CameraTarget
        {
            TrackingTarget = playerCameraTarget
        };
        playerTarget = playerCameraTarget;
        PlayerCamera.gameObject.SetActive(true);
    }
    
    public void SetPlayerBuildAvatar(Transform avatar)
    {
        if (PlayerBuildCamera == null)
        {
            return;
        }
        PlayerBuildCamera.Target = new Unity.Cinemachine.CameraTarget
        {
            TrackingTarget = avatar
        };
        PlayerCamera.Target = new Unity.Cinemachine.CameraTarget
        {
            TrackingTarget = null
        };
        PlayerBuildCamera.gameObject.SetActive(true);
        PlayerCamera.gameObject.SetActive(false);
    }
    
    public void ClearPlayerBuildAvatar()
    {
        if (PlayerBuildCamera != null)
        {
            PlayerBuildCamera.gameObject.SetActive(false);
            PlayerBuildCamera.Target = new Unity.Cinemachine.CameraTarget
            {
                TrackingTarget = null
            };
        }

        if (PlayerCamera != null)
        {
            PlayerCamera.Target = new Unity.Cinemachine.CameraTarget
            {
                TrackingTarget = playerTarget
            };
            PlayerCamera.gameObject.SetActive(true);
        }
    }
}
