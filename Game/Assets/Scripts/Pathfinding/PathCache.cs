using Pathfinding;
using UnityEngine;

public static class PathCache
{
    private static LRUCache<(Vector3 start, Vector3 end), Path> pathCache =
        new LRUCache<(Vector3, Vector3), Path>(capacity: 2048);

    private const float CellSize = 0.5f;

    public static Vector3 Quantize(Vector3 position)
    {
        return new Vector3(
            Mathf.Round(position.x / CellSize) * CellSize,
            Mathf.Round(position.y / CellSize) * CellSize,
            Mathf.Round(position.z / CellSize) * CellSize
        );
    }

    public static bool TryGetPath(Vector3 start, Vector3 end, out Path path)
    {
        var startKey = Quantize(start);
        var endKey   = Quantize(end);
        return pathCache.TryGet((startKey, endKey), out path);
    }

    public static void AddPath(Vector3 start, Vector3 end, Path path)
    {
        var startKey = Quantize(start);
        var endKey   = Quantize(end);
        pathCache.Add((startKey, endKey), path);
    }

    public static void ClearCache()
    {
        pathCache.Clear();
    }
}
