using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UniRx;

public class AnimationDeathBehavior : BaseMonoBehavior
{
    private AnimationActionAsset DeathAction;
    public GameObject DeathParticles;
    public Vector2 DeathForce;
    public bool DestroyOnDeath = true;
    private Combatant combatant;
    private float DestroyTime = 3f;
    private bool dead = false;

    void Start()
    {
        combatant = GetComponent<Combatant>();
        DeathAction = combatant.Weapon.Animation.Death;
        AddDisposable(combatant.HealthManager.OnDeathObservable.Subscribe(o =>
        {
            OnDeath(o);
        }, Debug.LogError));
    }

    private void OnDeath(DeathModel deathModel)
    {
        if (dead)
        {
            return;
        }
        dead = true;
        var nonInteractive = LayerMask.NameToLayer("NonInteractive");
        combatant.gameObject.layer = nonInteractive;

        foreach (Transform transform in gameObject.transform)
        {
            transform.gameObject.layer = nonInteractive;
        }

        combatant.MovementBehavior.SetToNonInteractive();
        combatant.MovementBehavior.FastRotate((deathModel.DamageModel.Force * -1).normalized);
        combatant.ActionManager.Execute(DeathAction, true);

        if (DeathForce != Vector2.zero)
        {
            StartCoroutine(ApplyDeathForce(deathModel.DamageModel.GetDirection()));
        }

        if (DestroyOnDeath)
        {
            StartCoroutine(DestroyObject());
        }
    }

    private IEnumerator ApplyDeathForce(Vector3 damageDirection)
    {
        yield return null;

        combatant.MovementBehavior.Launch(new Vector3(damageDirection.x * DeathForce.x, damageDirection.y + DeathForce.y, damageDirection.z * DeathForce.x), true);
    }

    private IEnumerator DestroyObject()
    {
        yield return new WaitForSeconds(DestroyTime);
        Toolbox.Instance.Get<ParticleObjectPool>().Play(DeathParticles, combatant.transform.position); 
        Destroy(gameObject);
    }

}
