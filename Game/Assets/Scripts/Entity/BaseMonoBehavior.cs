using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UniRx;

public class BaseMonoBehavior : MonoBehaviour
{
    private CompositeDisposable compositeDisposable = new CompositeDisposable();

    protected virtual void OnDestroy()
    {
        compositeDisposable.Dispose();
    }

    protected void AddDisposable(System.IDisposable disposable)
    {
        if (disposable == null)
        {
            return;
        }
        compositeDisposable.Add(disposable);
    }

    protected void RemoveDisposable(System.IDisposable disposable)
    {
        if (disposable == null)
        {
            return;
        }
        compositeDisposable.Remove(disposable);
    }

}
