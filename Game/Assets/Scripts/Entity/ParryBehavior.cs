using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ParryBehavior : BaseMonoBehavior
{
    private Combatant combatant;

    private Vector2 parryHitForce = new Vector2(3f, 0.3f);

    void Start()
    {
        combatant = GetComponent<Combatant>();
    }

    public bool TryParry()
    {
        var parryAction = combatant.Weapon.Animation.Parry;
        var model = combatant.ActionManager.Execute(parryAction);
        var success = model != null;
        if (success)
        {
            HandleParryForce();
        }
        return success;
    }

    private void HandleParryForce()
    {
        combatant.MovementBehavior.Launch(transform.forward * -1 * parryHitForce, true);
    }
}
