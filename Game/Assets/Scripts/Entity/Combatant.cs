using ECM2;
using UnityEngine;
using UniRx;

public class Combatant : BaseMonoBehavior
{
    public Allegiance Allegiance = Allegiance.Enemy;
    
    [HideInInspector]
    public ActionManager ActionManager;
    
    [HideInInspector]
    public AnimationManager AnimationManager;

    [HideInInspector]
    public CharacterMovementController MovementController;

    [HideInInspector]
    public CharacterMovement Movement;

    [HideInInspector]
    public CapsuleCollider MainCollider;

    [HideInInspector] 
    public TargetProvider TargetProvider;

    [HideInInspector] 
    public IMovementProvider MovementProvider;
    
    [HideInInspector] 
    public EquipmentManager EquipmentManager;
    
    [HideInInspector]
    public HealthManager HealthManager;
    
    [HideInInspector]
    public DamageManager DamageManager;
    
    [HideInInspector]
    public ParryBehavior ParryBehavior;

    [HideInInspector] 
    public MovementBehavior MovementBehavior;
    
    private bool isPlayer = false;
    
    public bool IsDead
    {
        get
        {
            return HealthManager.IsDead();
        }
    }

    public bool IsPlayer
    {
        get
        {
            return isPlayer;
        }
    }
    
    public bool IsNPC
    {
        get
        {
            return !isPlayer;
        }
    }
    
    public bool IsInvulnerable
    {
        get
        {
            return HealthManager.IsInvulnerable;
        }
    }

    public Vector3 MovementDirection
    {
        get
        {
            return MovementProvider.MovementDirection;
        }
    }

    public bool IsGrounded => MovementBehavior.IsGrounded;

    public WeaponAsset Weapon => EquipmentManager.Weapon;
    
    private Subject<Unit> onDestroySubject = new Subject<Unit>();
    public System.IObservable<Unit> OnDestroyObservable => onDestroySubject;

    void Awake()
    {
        ActionManager = GetComponent<ActionManager>();
        MovementController = GetComponent<CharacterMovementController>();
        Movement = GetComponent<CharacterMovement>();
        MovementBehavior = GetComponent<MovementBehavior>();
        MainCollider = GetComponent<CapsuleCollider>();
        AnimationManager = GetComponent<AnimationManager>();
        MovementProvider = GetComponent<IMovementProvider>();
        TargetProvider = GetComponent<TargetProvider>();
        EquipmentManager = GetComponent<EquipmentManager>();
        DamageManager = GetComponent<DamageManager>();
        HealthManager = GetComponent<HealthManager>();
        ParryBehavior = GetComponent<ParryBehavior>();
        isPlayer = GetComponent<PlayerController>() != null;
    }
    
    protected override void OnDestroy()
    {
        onDestroySubject.OnNext(new Unit());
        base.OnDestroy();
    }
}
