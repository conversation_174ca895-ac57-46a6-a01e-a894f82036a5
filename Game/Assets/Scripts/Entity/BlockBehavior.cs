using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UniRx;

public class BlockBehavior : BaseMonoBehavior
{
    private Combatant combatant;

    private float blockStartTime = 0f;

    public bool InParryWindow(float parryTime = 0.2f)
    {
        if (blockStartTime <= 0f)
        {
            return false;
        }

        return (Time.time - blockStartTime) <= parryTime;
    }

    private void Start()
    {
        this.combatant = GetComponent<Combatant>();
        var blockHitAction = combatant.Weapon.Animation.BlockHit;
        AddDisposable(combatant.DamageManager.OnHurtObservable.Subscribe(o => {
            HandleBlockHit(o);
        }, Debug.LogError));

        AddDisposable(combatant.ActionManager.ActionObservable.Subscribe(o => {
            if (o.Blocking && o.ActionAsset != blockHitAction)
            {
                blockStartTime = Time.time;
            }
            else if (!o.ActionAsset.Blocking)
            {
                blockStartTime = 0f;
            }
        }, Debug.LogError));
    }

    private void HandleBlockHit(DamageModel damageModel)
    {
        if (damageModel.Blocked)
        {
            if (damageModel.Originator != null)
            {
                combatant.MovementBehavior.FastRotate(damageModel.Originator.transform);
            }
            var current = combatant.ActionManager.Current;
            var blockHitAction = combatant.Weapon.Animation.BlockHit;
            var model = blockHitAction.GenerateModel();
            if (current != null && current.Blocking)
            {
                model.NextAction = current.NextAction;
            }
            combatant.ActionManager.Execute(model);
        }
    }

    public void HandleBlock(bool blocking, AnimationActionAsset blockAction)
    {
        if (blockAction == null)
        {
            return;
        }

        var current = combatant.ActionManager.Current;
        if (current == null)
        {
            return;
        }

        if (blocking)
        {
            if (current.ActionAsset != blockAction)
            {
                combatant.ActionManager.Execute(blockAction);
            }
        }
        else
        {
            if (current.ActionAsset == blockAction)
            {
                current.MakeInteruptable = true;
            }
        }
    }
}
