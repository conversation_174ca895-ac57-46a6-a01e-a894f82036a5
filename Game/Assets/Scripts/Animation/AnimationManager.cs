using System.Collections;
using Animancer;
using UnityEngine;
using UniRx;

public class AnimationManager : BaseMonoBehavior
{ 
    private const float attackHoldAnimationSpeedMultiplier = 0.15f;
    private const float defaultAnimationSpeed = 1f;
    private AnimancerComponent animator;
    private AnimationEventEmitter animationEventEmitter;

    private BehaviorSubject<AnimationChangeData> currentAnimation = new BehaviorSubject<AnimationChangeData>(null);
    public System.IObservable<AnimationChangeData> AnimationObservable => currentAnimation.Where(o => o != null);

    
    private System.IObservable<string> _cachedEventStream;
    public System.IObservable<string> eventStream 
        => _cachedEventStream ?? Observable.Empty<string>();

    public AnimationModel Current => currentAnimation.Value?.Current;

    private Coroutine HitStopCoroutine;
    private Coroutine MaxLoopCoroutine;
    private AnimancerState currentState;
    
    private void Awake()
    {
        animator = GetComponentInChildren<AnimancerComponent>();
        animationEventEmitter = GetComponentInChildren<AnimationEventEmitter>();

        if (animationEventEmitter != null)
        {
            _cachedEventStream = animationEventEmitter.EventStream
                .Where(o => Current != null && o.animatorClipInfo.clip == Current.Clip)
                .Select(evt => evt.stringParameter)
                .Publish()
                .RefCount();
        }
    }
    
    public void Play(AnimationModel newAnimation)
    {
        if (animator == null)
        {
            animator = GetComponentInChildren<AnimancerComponent>();
        }

        ClearMaxLoopCoroutine();
        ResetSpeed();

        var targetClip = newAnimation.Clip;
        // var currentClip = animator.States.Current?.Clip;
        // var fadeMode = (currentClip == targetClip) ? FadeMode.FromStart : FadeMode.FixedSpeed;

        currentState = animator.Play(targetClip, newAnimation.MixSpeed, FadeMode.FromStart);
        currentState.Speed = defaultAnimationSpeed * newAnimation.SpeedModifier;

        if (newAnimation.UseEarlyStartPercent)
        {
            currentState.NormalizedTime = newAnimation.EarlyStartPercent;
        }

        TriggerAnimationInterrupted();

        var previous = Current;
        currentAnimation.OnNext(new AnimationChangeData(newAnimation));
        AnimationModelPool.Release(previous);

        currentState.Events(this).OnEnd = TriggerAnimationEnd;

        if (newAnimation.MaxLoopTime > 0f)
        {
            MaxLoopCoroutine = StartCoroutine(MaxLoopTimer(newAnimation.MaxLoopTime, newAnimation));
        }
    }

    private void TriggerAnimationEnd()
    {
        if (Current == null)
        {
            return;
        }

        var onEnd = Current.OnAnimationComplete;
        if (onEnd == null)
        {
            return;
        }

        Current.OnAnimationComplete = null;
        onEnd();
    }

    private void TriggerAnimationInterrupted()
    {
        if (Current == null)
        {
            return;
        }

        var onInterrupt = Current.OnAnimationInterrupted;
        if (onInterrupt == null)
        {
            return;
        }

        Current.OnAnimationInterrupted = null;
        onInterrupt();
    }

    public void TriggerHitStop(float delay = 0f)
    {
        if (currentState == null)
        {
            return;
        }

        if (HitStopCoroutine != null)
        {
            return;
        }

        if (Current != null && Current.Holding)
        {
            return;
        }

        HitStopCoroutine = StartCoroutine(HitStop(currentState, delay));
    }

    public void TriggerHitStopHurt()
    {
        TriggerHitStop(0.1f);
    }

    public bool HoldAnimation()
    {
        if (currentState == null)
        {
            return false;
        }

        if (Current == null)
        {
            return false;
        }

        if (Current.Holding)
        {
            return false;
        }

        if (HitStopCoroutine != null)
        {
            StopCoroutine(HitStopCoroutine);
            HitStopCoroutine = null;
        }
        Current.Holding = true;
        UpdateSpeed(defaultAnimationSpeed * attackHoldAnimationSpeedMultiplier);
        return true;
    }

    public void HoldReleaseAnimation()
    {
        if (currentState == null)
        {
            return;
        }

        if (Current == null)
        {
            return;
        }

        if (!Current.Holding)
        {
            return;
        }

        Current.Holding = false;
        ResetSpeed();
    }

    private void ResetSpeed()
    {
        UpdateSpeed(defaultAnimationSpeed);
    }

    private IEnumerator HitStop(AnimancerState state, float delay)
    {
        yield return new WaitForSeconds(delay);
        UpdateSpeed(0);
        yield return new WaitForSeconds(0.1f);
        UpdateSpeed(defaultAnimationSpeed * 0.1f);
        yield return new WaitForSeconds(0.075f);
        UpdateSpeed(defaultAnimationSpeed);
        HitStopCoroutine = null;
    }

    private IEnumerator MaxLoopTimer(float maxTime, AnimationModel animationModel)
    {
        yield return new WaitForSeconds(maxTime);
        if (currentAnimation.Value?.Current == animationModel)
        {
            TriggerAnimationInterrupted();
            TriggerAnimationEnd();
        }
        ClearMaxLoopCoroutine();
    }

    private void ClearMaxLoopCoroutine()
    {
        if (MaxLoopCoroutine != null)
        {
            StopCoroutine(MaxLoopCoroutine);
        }

        MaxLoopCoroutine = null;
    }
    
    private void UpdateSpeed(float speed)
    {
        if (animator.States == null || animator.States.Current == null) return;
        var state = animator.States.Current;
        state.Speed = speed;
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        var current  = Current;
        if (current != null)
        {
            AnimationModelPool.Release(current);
        }
    }
}

public class AnimationChangeData
{
    public AnimationModel Current;

    public AnimationChangeData(AnimationModel current)
    {
        this.Current = current;
    }
}
