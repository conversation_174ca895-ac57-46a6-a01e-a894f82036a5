using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(menuName = "Animation/Event/VectorEvent")]
public class AnimationVectorEvent : AnimationEventComponent
{
    public float Force = 0f;
    public float UpForce = 0f;
    public DirectionEnum Direction = DirectionEnum.Forward;
    public bool RotateTowardsCurrentTarget = false;
    public bool StopVelocity = true;
    public bool OverrideVerticalVelocity = true;

    public override void OnEvent(Combatant combatant, AnimationActionModel model)
    {
        var direction = combatant.transform.forward;

        if (RotateTowardsCurrentTarget)
        {
            combatant.TargetProvider.GetCloseTarget(callback: target =>
            {
                if (target != null)
                {
                    combatant.MovementBehavior.FastRotate(target.transform);
                    direction = VectorHelpers.GetDirectionXZ(combatant.transform, target.transform);
                }
            });
        }
        else if (Direction == DirectionEnum.Backward)
        {
            direction *= -1;
        }
        else if (Direction == DirectionEnum.MovementDirection)
        {
            var movementDirection = combatant.MovementDirection;
            if (movementDirection != Vector3.zero)
            {
                direction = movementDirection;
            }
        }
        var force = Force * direction;
        var adjusted = new Vector3(force.x, force.y + UpForce, force.z);

        if (StopVelocity)
        {
            combatant.MovementBehavior.StopVelocity();
        }

        combatant.MovementBehavior.Launch(adjusted, OverrideVerticalVelocity);
    }

}

public enum DirectionEnum
{
    Forward,
    Backward,
    MovementDirection
}