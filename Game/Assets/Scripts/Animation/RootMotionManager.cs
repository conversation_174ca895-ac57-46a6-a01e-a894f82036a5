using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UniRx;

public class RootMotionManager : BaseMonoBehavior
{
    public virtual Vector3 animationRootMotionVelocity { get; protected set; }
    public virtual Quaternion animationDeltaRotation => animator.deltaRotation;

    private Animator animator;
    private AnimationManager animationManager;

    private void Awake()
    {
        this.animator = GetComponent<Animator>();
        this.animationManager = GetComponentInParent<AnimationManager>();
    }

    private void Start()
    {
        AddDisposable(animationManager.AnimationObservable.Subscribe(o =>
        {
            if (!o.Current.RootMotion)
            {
                animationRootMotionVelocity = Vector3.zero;
            }
        }, Debug.LogError));
    }

    private void OnAnimatorMove()
    {
        if (animationManager.Current != null && animationManager.Current.RootMotion)
        {
            animationRootMotionVelocity = CalculateAnimationRootMotionVelocity();
        }
        else
        {
            animationRootMotionVelocity = Vector3.zero;
        }
    }

    protected virtual Vector3 CalculateAnimationRootMotionVelocity()
    {
        float deltaTime = Time.deltaTime;

        if (deltaTime > 0.0f)
            return animator.deltaPosition / deltaTime;

        return Vector3.zero;
    }
}
