using System.Collections.Generic;
using UnityEngine;

[CreateAssetMenu(menuName = "Animation/Asset")]
public class AnimationAsset : ScriptableObject
{
    public AnimationClip Clip;
    public float SpeedModifier = 1f;
    public float MixSpeed = 0.2f;
    public float MaxLoopTime = 0f;
    public float EarlyStartPercent = 0f;
    public bool RootMotion = false;
    public List<AnimationIKData> IKData;

    public AnimationModel GenerateModel()
    {
        return AnimationModelPool.Get(this);
    }

}

[System.Serializable]
public class AnimationIKData
{
    public IKType Type;
    public float MixSpeed = 0.2f;
}
