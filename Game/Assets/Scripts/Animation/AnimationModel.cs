using UnityEngine;

public class AnimationModel
{
    public AnimationAsset animationAsset;

    public AnimationClip Clip => animationAsset.Clip;
    public bool Looping => animationAsset.Clip.isLooping && MaxLoopTime == 0f;
    public float MaxLoopTime => animationAsset.MaxLoopTime;
    public float MixSpeed => animationAsset.MixSpeed * MixSpeedModifier;
    public bool RootMotion => animationAsset.RootMotion;
    public float EarlyStartPercent => animationAsset.EarlyStartPercent;
    public float SpeedModifier => animationAsset.SpeedModifier;
    public bool Holding = false;
    public bool UseEarlyStartPercent = false;
    public float MixSpeedModifier = 1f;

    public System.Action OnAnimationComplete { get; set; }
    public System.Action OnAnimationInterrupted { get; set; }

    public AnimationModel(AnimationAsset animationAsset)
    {
        this.animationAsset = animationAsset;
    }

    public void Reset(AnimationAsset asset)
    {
        animationAsset = asset;
        Holding = false;
        UseEarlyStartPercent = false;
        MixSpeedModifier = 1f;
        OnAnimationComplete = null;
        OnAnimationInterrupted = null;
    }
}
