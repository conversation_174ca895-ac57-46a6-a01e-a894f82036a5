using System;
using Animancer;
using UnityEngine;

public class LegacyAnimationEventHandler : MonoBehaviour
{
    public void FootL() {
        // TODO handle animation events
    }
    
    public void FootR() {
        // TODO handle animation events
    }

    public void Hit()
    {
        // TODO handle animation events
    }

    public void ToggleHitBox()
    {
        // TODO handle animation events
    }

    public void DodgeForce()
    {
        // TODO handle animation events
    }
    
    public void AttackForce()
    {
        // TODO handle animation events
    }

    public void SoftFinish()
    {
        // TODO handle animation events
    }

    public void SoftFinishAction()
    {
        // TODO handle animation events
    }
}
