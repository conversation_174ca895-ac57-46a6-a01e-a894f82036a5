using System.Collections;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine;

[CreateAssetMenu(menuName = "Animation/Event/WeaponFX")]
public class WeaponFXEvent : AnimationEventComponent
{
    public override void OnEvent(Combatant combatant, AnimationActionModel model)
    {
        var weaponFx = combatant.EquipmentManager.WeaponBehavior.GetComponent<WeaponFX>();
        if(weaponFx != null)
        {
            weaponFx.SpawnSwipeFX();
        }
    }
}
