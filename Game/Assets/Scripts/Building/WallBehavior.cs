using System.Collections.Generic;
using UnityEngine;

namespace Building
{
    public class WallBehavior : MonoBehaviour
    {
        [Header("Wall Connection Objects")]
        public List<GameObject> NorthObjects;
        public List<GameObject> EastObjects;
        public List<GameObject> SouthObjects;
        public List<GameObject> WestObjects;

        [Header("Connected Walls")]
        [SerializeField] private Dictionary<Direction, WallBehavior> connectedWalls;

        private void Awake()
        {
            connectedWalls = new Dictionary<Direction, WallBehavior>();
        }

        /// <summary>
        /// Sets the connected walls for this wall
        /// </summary>
        /// <param name="connections">Dictionary of connected walls by direction</param>
        public void SetConnectedWalls(Dictionary<Direction, WallBehavior> connections)
        {
            connectedWalls = connections;
            UpdateWallConnections();
        }

        /// <summary>
        /// Gets the wall connected in the specified direction
        /// </summary>
        /// <param name="direction">Direction to check</param>
        /// <returns>Connected wall or null if none</returns>
        public WallBehavior GetConnectedWall(Direction direction)
        {
            return connectedWalls.TryGetValue(direction, out var wall) ? wall : null;
        }

        /// <summary>
        /// Checks if there's a wall connected in the specified direction
        /// </summary>
        /// <param name="direction">Direction to check</param>
        /// <returns>True if connected, false otherwise</returns>
        public bool HasConnectionInDirection(Direction direction)
        {
            return GetConnectedWall(direction) != null;
        }

        /// <summary>
        /// Gets all connected walls
        /// </summary>
        /// <returns>Dictionary of all connections</returns>
        public Dictionary<Direction, WallBehavior> GetAllConnections()
        {
            return new Dictionary<Direction, WallBehavior>(connectedWalls);
        }

        /// <summary>
        /// Updates wall visual/behavior based on connections
        /// </summary>
        private void UpdateWallConnections()
        {
            // The Direction enum represents logical directions relative to the wall's local space
            // The object lists represent physical objects in the wall's local coordinate system
            // These should always map directly regardless of world rotation
            UpdateDirectionObjects(Direction.North, NorthObjects);
            UpdateDirectionObjects(Direction.East, EastObjects);
            UpdateDirectionObjects(Direction.South, SouthObjects);
            UpdateDirectionObjects(Direction.West, WestObjects);

            // LogConnections();
        }

        /// <summary>
        /// Updates objects for a specific direction based on connection status
        /// </summary>
        /// <param name="direction">Direction to update</param>
        /// <param name="objects">List of objects for this direction</param>
        private void UpdateDirectionObjects(Direction direction, List<GameObject> objects)
        {
            bool hasConnection = HasConnectionInDirection(direction);

            if (objects != null)
            {
                foreach (var obj in objects)
                {
                    if (obj != null)
                    {
                        // Enable objects when there's a connection, disable when there isn't
                        // You can customize this logic based on your needs
                        obj.SetActive(!hasConnection);
                    }
                }
            }
        }

        /// <summary>
        /// Logs current connections for debugging
        /// </summary>
        private void LogConnections()
        {
            bool hasNorth = HasConnectionInDirection(Direction.North);
            bool hasSouth = HasConnectionInDirection(Direction.South);
            bool hasEast = HasConnectionInDirection(Direction.East);
            bool hasWest = HasConnectionInDirection(Direction.West);

            Debug.Log($"Wall '{gameObject.name}' connections - North: {hasNorth}, South: {hasSouth}, East: {hasEast}, West: {hasWest}");
        }
    }
}
