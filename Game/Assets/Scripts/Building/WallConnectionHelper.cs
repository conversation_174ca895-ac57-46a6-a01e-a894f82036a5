using System.Collections.Generic;
using UnityEngine;

namespace Building
{
    /// <summary>
    /// Helper class for managing wall connections and detecting adjacent walls
    /// </summary>
    public static class WallConnectionHelper
    {
        /// <summary>
        /// Sets up connections for a newly placed wall
        /// </summary>
        /// <param name="structure">The structure that was just placed</param>
        /// <param name="gridSize">Size of the grid for connection detection</param>
        /// <param name="colliderBuffer">Buffer for physics overlap detection</param>
        /// <param name="layerMask">Layer mask for structure detection</param>
        public static void HandleWallConnections(GameObject structure, float gridSize, Collider[] colliderBuffer, int layerMask)
        {
            var wall = structure.GetComponent<WallBehavior>();
            if (wall != null)
            {
                var connectedWalls = GetConnectedWalls(structure, gridSize, colliderBuffer, layerMask);
                wall.SetConnectedWalls(connectedWalls);
                
                // Also update any existing connected walls to recognize this new wall
                UpdateConnectedWalls(wall, connectedWalls);
            }
        }

        /// <summary>
        /// Gets all walls connected to the placed structure in the four cardinal directions
        /// </summary>
        /// <param name="placedStructure">The structure that was just placed</param>
        /// <param name="gridSize">Size of the grid for connection detection</param>
        /// <param name="colliderBuffer">Buffer for physics overlap detection</param>
        /// <param name="layerMask">Layer mask for structure detection</param>
        /// <returns>Dictionary with direction as key and connected WallBehavior as value (null if no connection)</returns>
        public static Dictionary<Direction, WallBehavior> GetConnectedWalls(GameObject placedStructure, float gridSize, Collider[] colliderBuffer, int layerMask)
        {
            var connections = new Dictionary<Direction, WallBehavior>
            {
                { Direction.North, null },
                { Direction.South, null },
                { Direction.East, null },
                { Direction.West, null }
            };

            var structurePosition = placedStructure.transform.position;
            var structureBounds = CalculateWorldBounds(placedStructure);
            var placedWall = placedStructure.GetComponent<WallBehavior>();
            var structureRotation = placedStructure.transform.rotation;
            
            // Define the four cardinal directions in local space (relative to the structure)
            var localDirections = new Dictionary<Direction, Vector3>
            {
                { Direction.North, Vector3.forward },
                { Direction.South, Vector3.back },
                { Direction.East, Vector3.right },
                { Direction.West, Vector3.left }
            };

            foreach (var direction in localDirections)
            {
                // Transform the local direction to world space based on the structure's rotation
                var worldDirection = structureRotation * direction.Value;
                var searchPosition = structurePosition + worldDirection * gridSize;
                var connectedWall = FindWallAtPosition(searchPosition, structureBounds.size, placedWall, colliderBuffer, layerMask);
                connections[direction.Key] = connectedWall;
            }

            return connections;
        }

        /// <summary>
        /// Updates existing connected walls to recognize the newly placed wall
        /// </summary>
        /// <param name="newWall">The newly placed wall</param>
        /// <param name="connections">The connections of the new wall</param>
        private static void UpdateConnectedWalls(WallBehavior newWall, Dictionary<Direction, WallBehavior> connections)
        {
            foreach (var connection in connections)
            {
                if (connection.Value != null)
                {
                    // Calculate which direction on the connected wall points back to the new wall
                    var connectionDirection = GetConnectionDirection(newWall.transform, connection.Value.transform, connection.Key);
                    var connectedWallConnections = connection.Value.GetAllConnections();
                    connectedWallConnections[connectionDirection] = newWall;
                    connection.Value.SetConnectedWalls(connectedWallConnections);
                }
            }
        }

        /// <summary>
        /// Determines which direction on the connected wall points back to the new wall
        /// </summary>
        /// <param name="newWallTransform">Transform of the newly placed wall</param>
        /// <param name="connectedWallTransform">Transform of the connected wall</param>
        /// <param name="newWallDirection">Direction from new wall to connected wall</param>
        /// <returns>Direction on connected wall that points back to new wall</returns>
        private static Direction GetConnectionDirection(Transform newWallTransform, Transform connectedWallTransform, Direction newWallDirection)
        {
            // Calculate the world direction from connected wall to new wall
            var worldDirectionToNewWall = (newWallTransform.position - connectedWallTransform.position).normalized;
            
            // Transform this world direction into the connected wall's local space
            var localDirectionToNewWall = Quaternion.Inverse(connectedWallTransform.rotation) * worldDirectionToNewWall;
            
            // Find which cardinal direction this is closest to
            return GetClosestDirection(localDirectionToNewWall);
        }

        /// <summary>
        /// Gets the closest cardinal direction to the given vector
        /// </summary>
        /// <param name="direction">Direction vector to check</param>
        /// <returns>Closest cardinal direction</returns>
        private static Direction GetClosestDirection(Vector3 direction)
        {
            var directions = new Dictionary<Direction, Vector3>
            {
                { Direction.North, Vector3.forward },
                { Direction.South, Vector3.back },
                { Direction.East, Vector3.right },
                { Direction.West, Vector3.left }
            };

            Direction closestDirection = Direction.North;
            float closestDot = float.MinValue;

            foreach (var dir in directions)
            {
                float dot = Vector3.Dot(direction.normalized, dir.Value);
                if (dot > closestDot)
                {
                    closestDot = dot;
                    closestDirection = dir.Key;
                }
            }

            return closestDirection;
        }

        /// <summary>
        /// Finds a wall at the specified position, excluding the specified wall
        /// </summary>
        /// <param name="position">World position to search</param>
        /// <param name="searchSize">Size of the search area</param>
        /// <param name="excludeWall">Wall to exclude from the search (typically the wall being placed)</param>
        /// <param name="colliderBuffer">Buffer for physics overlap detection</param>
        /// <param name="layerMask">Layer mask for structure detection</param>
        /// <returns>WallBehavior component if found, null otherwise</returns>
        private static WallBehavior FindWallAtPosition(Vector3 position, Vector3 searchSize, WallBehavior excludeWall, Collider[] colliderBuffer, int layerMask)
        {
            // Use a slightly smaller search area to avoid overlapping detections
            var halfExtents = searchSize * 0.4f;
            
            int hitCount = Physics.OverlapBoxNonAlloc(
                position,
                halfExtents,
                colliderBuffer,
                Quaternion.identity,
                layerMask,
                QueryTriggerInteraction.Ignore
            );

            for (int i = 0; i < hitCount; i++)
            {
                var wall = colliderBuffer[i].GetComponent<WallBehavior>();
                if (wall != null && wall != excludeWall)
                {
                    return wall;
                }
            }

            return null;
        }

        /// <summary>
        /// Calculates the world bounds of a GameObject
        /// </summary>
        /// <param name="obj">GameObject to calculate bounds for</param>
        /// <returns>World bounds of the object</returns>
        private static Bounds CalculateWorldBounds(GameObject obj)
        {
            var renderers = obj.GetComponentsInChildren<Renderer>();
            if (renderers.Length == 0)
                return new Bounds(obj.transform.position, Vector3.one);

            var bounds = renderers[0].bounds;
            for (int i = 1; i < renderers.Length; i++)
            {
                bounds.Encapsulate(renderers[i].bounds);
            }
            return bounds;
        }
    }
}
