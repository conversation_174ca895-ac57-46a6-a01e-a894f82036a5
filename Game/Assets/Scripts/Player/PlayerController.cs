using ECM2;
using UnityEngine;
using UniRx;

public class PlayerController : BaseMonoBehavior, IMovementProvider
{
    [HideInInspector]
    public PlayerControlsManager Controls;

    public Transform CameraTarget;
    private Transform CameraTransform;
    private Combatant combatant;
    private PlayerJumpBehavior jumpBehavior;
    private PlayerDodgeBehavior dodgeBehavior;
    private MovementBehavior movementBehavior;
    private PlayerAttackBehavior playerAttackBehavior;
    private PlayerBlockBehavior playerBlockBehavior;
    private PlayerModeSwitchBehavior modeBehavior;
    
    public Vector3 MovementDirection
    {
        get
        {
            var input = InputVector;
            var horizontal = input.x;
            var vertical = input.y;
            var movementDirection = Vector3.zero;

            movementDirection += Vector3.right * horizontal;
            movementDirection += Vector3.forward * vertical;
            if (CameraTransform)
            {
                movementDirection = movementDirection.relativeTo(CameraTransform);
            }

            return movementDirection.normalized;
        }
    }
    
    public Vector2 InputVector
    {
        get
        {
            if (!modeBehavior.IsPlayer)
            {
                return Vector3.zero;
            }
            return Controls.InputVector;
        }
    }
    
    void Awake()
    {
        if (Controls == null)
        {
            Controls = ControllerInputManager.Instance.Player1ControlsManager;
        }
    }

    private void Start()
    {
        PlayerCameraManager.Instance.SetPlayerCameraTarget(CameraTarget);
        CameraTransform = PlayerCameraManager.Instance.MainCamera.transform;
        combatant = GetComponent<Combatant>();
        jumpBehavior = GetComponent<PlayerJumpBehavior>();
        movementBehavior = GetComponent<MovementBehavior>();
        dodgeBehavior = GetComponent<PlayerDodgeBehavior>();
        playerAttackBehavior = GetComponent<PlayerAttackBehavior>();
        playerBlockBehavior = GetComponent<PlayerBlockBehavior>();
        modeBehavior = GetComponent<PlayerModeSwitchBehavior>();
        
        AddDisposable(Controls.OnInputJump.Subscribe(o =>
        {
            if (!modeBehavior.IsPlayer)
            {
                return;
            }
            jumpBehavior.Jump();
        }, Debug.LogError));
        
        AddDisposable(Controls.OnInputDodgeRoll.Subscribe(o =>
        {
            if (!modeBehavior.IsPlayer)
            {
                return;
            }
            dodgeBehavior.TryDodge(MovementDirection);
        }, Debug.LogError));
        
        AddDisposable(Controls.OnInputAttack.Subscribe(o =>
        {
            if (!modeBehavior.IsPlayer)
            {
                return;
            }
            playerAttackBehavior.TryAttack();
        }, Debug.LogError));

        AddDisposable(Controls.OnInputBlock.Subscribe(o =>
        {
            if (!modeBehavior.IsPlayer)
            {
                return;
            }
            playerBlockBehavior.TryBlock();
        }, Debug.LogError));
        
        AddDisposable(Controls.OnInputModeSwitch.Subscribe(o =>
        {
            modeBehavior.Switch();
        }, Debug.LogError));
    }
    
    private void Update()
    {
        movementBehavior.Move(MovementDirection);
    }
}
