using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Physics;
using Unity.Transforms;
using UnityEngine;

public class TerrainColliderToEntity : MonoBehaviour
{
    private Entity entity;

    void OnEnable()
    {
        var entityManager = World.DefaultGameObjectInjectionWorld.EntityManager;
        var terrain = GetComponent<Terrain>();
        var unityCollider = GetComponent<UnityEngine.TerrainCollider>();

        if (terrain == null || unityCollider == null)
        {
            Debug.LogWarning("TerrainColliderToEntity requires both Terrain and TerrainCollider components.");
            return;
        }

        var terrainData = terrain.terrainData;
        int heightmapResolution = terrainData.heightmapResolution;
        float3 terrainSize = terrainData.size;

        // Flatten the 2D heightmap into a 1D array
        float[,] heights2D = terrainData.GetHeights(0, 0, heightmapResolution, heightmapResolution);
        var heights1D = new NativeArray<float>(heightmapResolution * heightmapResolution, Allocator.Temp);
        for (int z = 0; z < heightmapResolution; z++)
        {
            for (int x = 0; x < heightmapResolution; x++)
            {
                heights1D[z * heightmapResolution + x] = heights2D[z, x];
            }
        }

        // Define collision filter
        int unityLayer = gameObject.layer;
        uint belongsTo = 1u << unityLayer;
        uint collidesWith = LayerHelper.GetCollidesWithMask(unityLayer);
        var filter = new CollisionFilter
        {
            BelongsTo = belongsTo,
            CollidesWith = collidesWith,
            GroupIndex = 0
        };

        // Create the terrain collider
        var terrainCollider = Unity.Physics.TerrainCollider.Create(
            heights1D,
            new int2(heightmapResolution, heightmapResolution),
            terrainSize,
            Unity.Physics.TerrainCollider.CollisionMethod.Triangles,
            filter
        );

        heights1D.Dispose();

        // Create the entity and add components
        entity = entityManager.CreateEntity();

        entityManager.AddComponentData(entity, new LocalTransform
        {
            Position = transform.position,
            Rotation = transform.rotation,
            Scale = 1f
        });

        entityManager.AddComponentData(entity, new PhysicsCollider { Value = terrainCollider });
        entityManager.AddComponent<Simulate>(entity);
        entityManager.AddComponent<PhysicsWorldIndex>(entity);
    }
    
    void OnDisable()
    {
        EntityUtils.Destroy(entity);
        entity = default;
    }
}
