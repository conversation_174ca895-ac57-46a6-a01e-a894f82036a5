using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEngine;
using Unity.Physics;

public class MeshColliderToEntity : MonoBehaviour
{
    private Entity physicsEntity;

    void OnEnable()
    {
        var entityManager = World.DefaultGameObjectInjectionWorld.EntityManager;
        var meshCollider = GetComponent<UnityEngine.MeshCollider>();

        if (meshCollider == null || meshCollider.sharedMesh == null)
        {
            Debug.LogWarning("No MeshCollider with mesh found.");
            return;
        }
        
        // Determine collision filter from GameObject layer
        int unityLayer = gameObject.layer;
        uint belongsTo = 1u << unityLayer;
        uint collidesWith = LayerHelper.GetCollidesWithMask(unityLayer);

        var filter = new CollisionFilter
        {
            BelongsTo = belongsTo,
            CollidesWith = collidesWith,
            GroupIndex = 0
        };
        
        Mesh mesh = meshCollider.sharedMesh;

        // Extract vertices and triangles
        var vertices = mesh.vertices;
        var triangles = mesh.triangles;

        Vector3 worldScale = transform.lossyScale;
        var vertexList = new NativeArray<float3>(vertices.Length, Allocator.Temp);
        for (int i = 0; i < vertices.Length; i++)
        {
            Vector3 scaledVertex = Vector3.Scale(vertices[i], worldScale);
            vertexList[i] = scaledVertex;
        }

        var indexList = new NativeArray<int3>(triangles.Length / 3, Allocator.Temp);
        for (int i = 0; i < triangles.Length; i += 3)
        {
            indexList[i / 3] = new int3(triangles[i], triangles[i + 1], triangles[i + 2]);
        }
        
        var physicsMaterial = new Unity.Physics.Material
        {
            Friction = 0.5f,
            Restitution = 0.0f,
            CollisionResponse = CollisionResponsePolicy.Collide
        };

        // Create collider
        BlobAssetReference<Unity.Physics.Collider> entityCollider = Unity.Physics.MeshCollider.Create(vertexList, indexList, filter, physicsMaterial);

        // Cleanup
        vertexList.Dispose();
        indexList.Dispose();

        // Create the ECS entity
        physicsEntity = entityManager.CreateEntity();

        entityManager.AddComponentData(physicsEntity, LocalTransform.FromPositionRotation(
            transform.position, transform.rotation));

        entityManager.AddComponentData(physicsEntity, new PhysicsCollider
        {
            Value = entityCollider
        });
        entityManager.AddComponent<Simulate>(physicsEntity);
        entityManager.AddComponent<PhysicsWorldIndex>(physicsEntity);
    }

    void OnDisable()
    {
        EntityUtils.Destroy(physicsEntity);
        physicsEntity = default;
    }
}
