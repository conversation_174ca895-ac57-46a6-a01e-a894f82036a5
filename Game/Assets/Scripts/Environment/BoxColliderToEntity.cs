using Unity.Entities;
using Unity.Mathematics;
using Unity.Physics;
using Unity.Transforms;
using UnityEngine;

public class BoxColliderToEntity : MonoBehaviour
{
    private Entity entity;

    void OnEnable()
    {
        var entityManager = World.DefaultGameObjectInjectionWorld.EntityManager;

        var unityBox = GetComponent<UnityEngine.BoxCollider>();
        if (unityBox == null)
        {
            Debug.LogWarning("No BoxCollider found on GameObject.");
            return;
        }
        
        int unityLayer = gameObject.layer;
        uint belongsTo = 1u << unityLayer;
        uint collidesWith = LayerHelper.GetCollidesWithMask(unityLayer);
        
        // Grab world-space scale
        Vector3 worldScale = transform.lossyScale;

        // Create BoxGeometry
        var boxGeometry = new BoxGeometry
        {
            Center = unityBox.center,
            Size = Vector3.Scale(unityBox.size, worldScale),
            Orientation = quaternion.identity,
            BevelRadius = 0.01f
        };

        // Create Collision Filter
        var filter = new CollisionFilter
        {
            BelongsTo = belongsTo,
            CollidesWith = collidesWith,
            GroupIndex = 0
        };

        // Create Physics Material
        var material = new Unity.Physics.Material
        {
            Friction = 0.5f,
            Restitution = 0.0f
        };

        // Create the ECS collider
        var entityCollider = Unity.Physics.BoxCollider.Create(boxGeometry, filter, material);

        // Create ECS entity
        entity = entityManager.CreateEntity();

        entityManager.AddComponentData(entity, new PhysicsCollider
        {
            Value = entityCollider
        });
        
        entityManager.AddComponentData(entity, new LocalTransform
        {
            Position = transform.position,
            Rotation = transform.rotation,
            Scale = 1f
        });

        entityManager.AddComponent<Simulate>(entity);
        entityManager.AddComponent<PhysicsWorldIndex>(entity);
    }
    
    void OnDisable()
    {
        EntityUtils.Destroy(entity);
        entity = default;
    }
}