using System;
using Unity.Collections;
using Unity.Jobs;
using UnityEngine;

public class OverlapSphereBatcher : IDisposable
{
    private readonly int maxQueries;
    private readonly int maxHitsPerQuery;
    private NativeArray<OverlapSphereCommand> commands;
    private NativeArray<ColliderHit> results;
    private int nextQueryIndex;
    
    public int Capacity => maxQueries;

    public OverlapSphereBatcher(int maxQueries, int maxHitsPerQuery, Allocator allocator = Allocator.TempJob)
    {
        this.maxQueries = maxQueries;
        this.maxHitsPerQuery = maxHitsPerQuery;
        commands = new NativeArray<OverlapSphereCommand>(maxQueries, allocator);
        results = new NativeArray<ColliderHit>(maxQueries * maxHitsPerQuery, allocator);
        nextQueryIndex = 0;
    }

    /// <summary>
    /// Enqueue one OverlapSphere query. Returns its index for later retrieval.
    /// </summary>
    /// <param name="center">Sphere center</param>
    /// <param name="radius">Sphere radius</param>
    /// <param name="layerMask">Physics layers to hit</param>
    /// <param name="hitTriggers">Whether to hit trigger colliders</param>
    /// <param name="hitBackfaces">Whether to hit backfaces (rarely needed)</param>
    /// <param name="hitMultipleFaces">Whether to report multiple faces per collider</param>
    public int EnqueueQuery(
        Vector3 center,
        float radius,
        LayerMask layerMask,
        QueryTriggerInteraction hitTriggers = QueryTriggerInteraction.UseGlobal,
        bool hitBackfaces = false,
        bool hitMultipleFaces = false
    )
    {
        if (nextQueryIndex >= maxQueries)
            throw new InvalidOperationException($"Cannot enqueue more than {maxQueries} queries");

        var qp = QueryParameters.Default;
        qp.layerMask = layerMask;
        qp.hitTriggers = hitTriggers;
        qp.hitBackfaces = hitBackfaces;
        qp.hitMultipleFaces = hitMultipleFaces;

        commands[nextQueryIndex] = new OverlapSphereCommand(center, radius, qp);
        return nextQueryIndex++;
    }

    /// <summary>
    /// Schedule all enqueued OverlapSphereCommand calls in parallel, then block until done.
    /// </summary>
    public void ScheduleAndComplete(int minCommandsPerJob = 32)
    {
        JobHandle handle = OverlapSphereCommand.ScheduleBatch(
            commands,
            results,
            minCommandsPerJob,
            maxHitsPerQuery
        );
        handle.Complete();
    }

    /// <summary>
    /// Pulls out the valid hits for one query index.
    /// Returns the hit count and hands you back a NativeArray you must Dispose().
    /// </summary>
    public int GetHitsForQuery(int queryIndex, out NativeArray<ColliderHit> hitBuffer)
    {
        if (queryIndex < 0 || queryIndex >= nextQueryIndex)
            throw new ArgumentOutOfRangeException(nameof(queryIndex));

        int start = queryIndex * maxHitsPerQuery;
        // Unity writes a null-collider sentinel when no more hits
        int count = 0;
        for (int i = 0; i < maxHitsPerQuery; i++)
        {
            if (results[start + i].collider == null)
                break;
            count++;
        }

        hitBuffer = new NativeArray<ColliderHit>(count, Allocator.Temp);
        if (count > 0)
            NativeArray<ColliderHit>.Copy(results, start, hitBuffer, 0, count);

        return count;
    }
    
    public void Reset()
    {
        nextQueryIndex = 0;
    }

    public void Dispose()
    {
        if (commands.IsCreated) commands.Dispose();
        if (results.IsCreated) results.Dispose();
    }
}