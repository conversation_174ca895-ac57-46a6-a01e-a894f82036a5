using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class Utils
{
    public static Vector3 GenerateSmallImpulseVector()
    {
        return new Vector3(GetRandomSmallImpulseValue(), GetRandomSmallImpulseValue(), GetRandomSmallImpulseValue());
    }

    private static float GetRandomSmallImpulseValue()
    {
        var num = Random.Range(0.03f, 0.05f);
        if (Random.value < 0.5f)
        {
            num *= -1f;
        }
        return num;
    }
}
