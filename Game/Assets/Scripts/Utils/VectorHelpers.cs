using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class VectorHelpers
{
    public static Vector3 GetDirectionXZ(Transform start, Transform end)
    {
        var direction = (end.position - start.position).normalized;
        return new Vector3(direction.x, 0f, direction.z);
    }

    public static bool IsLowInput(this Vector3 vector)
    {
        float lowInputValue = 0.2f;
        return Mathf.Abs(vector.x) < lowInputValue && Mathf.Abs(vector.y) < lowInputValue;
    }
}
