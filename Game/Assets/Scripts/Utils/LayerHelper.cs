using UnityEngine;

public static class LayerHelper
{
    public static readonly string GroundLayerName = "Ground";
    public static readonly string EntityLayerName = "Entity";
    public static readonly string DefaultLayerName = "Default";
    public static readonly string EmptyLayerName = "Empty";
    public static readonly string NonInteractive = "NonInteractive";
    
    public static readonly int DefaultLayer = LayerMask.NameToLayer(DefaultLayerName);
    public static readonly int DefaultLayerBitMask = LayerMask.GetMask(DefaultLayerName);
    public static readonly int GroundLayer = LayerMask.NameToLayer(GroundLayerName);
    public static readonly int GroundLayerBitMask = LayerMask.GetMask(GroundLayerName);
    public static readonly int EntityLayer = LayerMask.NameToLayer(EntityLayerName);
    public static readonly int EntityLayerBitMask = LayerMask.GetMask(EntityLayerName);
    public static readonly int EmptyLayer = LayerMask.NameToLayer(EmptyLayerName);
    public static readonly int NonInteractiveLayer = LayerMask.NameToLayer(NonInteractive);
    
    public static uint GetCollidesWithMask(int layer)
    {
        uint mask = 0;
        for (int i = 0; i < 32; i++)
        {
            if (!Physics.GetIgnoreLayerCollision(layer, i))
            {
                mask |= (uint)(1 << i);
            }
        }
        return mask;
    }
}
