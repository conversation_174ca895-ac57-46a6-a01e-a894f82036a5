using System;
using Unity.Collections;
using Unity.Jobs;
using UnityEngine;

/// <summary>
/// Batches multiple CapsuleCast commands via CapsulecastCommand.ScheduleBatch.
/// </summary>
public class CapsuleCastBatcher : IDisposable
{
    private readonly int maxQueries;
    private readonly int maxHits;
    private NativeArray<CapsulecastCommand> commands;
    private NativeArray<RaycastHit> results;
    private int nextIndex;

    public int Capacity => maxQueries;

    public CapsuleCastBatcher(int maxQueries, int maxHits, Allocator allocator = Allocator.TempJob)
    {
        this.maxQueries = maxQueries;
        this.maxHits = maxHits;
        commands = new NativeArray<CapsulecastCommand>(maxQueries, allocator);
        results = new NativeArray<RaycastHit>(maxQueries * maxHits, allocator);
        nextIndex = 0;
    }

    public void Reset() => nextIndex = 0;

    /// <summary>
    /// Enqueue one capsule‐cast query. Returns an index for result lookup.
    /// </summary>
    public int EnqueueQuery(
        Vector3 point1,
        Vector3 point2,
        float radius,
        Vector3 direction,
        float distance,
        LayerMask layerMask,
        QueryTriggerInteraction hitTriggers = QueryTriggerInteraction.UseGlobal,
        bool hitBackfaces = false,
        bool hitMultipleFaces = false
    )
    {
        if (nextIndex >= maxQueries)
            throw new InvalidOperationException($"Exceeded max capsule queries ({maxQueries})");

        var qp = QueryParameters.Default;
        qp.layerMask = layerMask;
        qp.hitTriggers = hitTriggers;
        qp.hitBackfaces = hitBackfaces;
        qp.hitMultipleFaces = hitMultipleFaces;

        commands[nextIndex] = new CapsulecastCommand(
            point1,
            point2,
            radius,
            direction,
            qp,
            distance
        );

        return nextIndex++;
    }

    /// <summary>
    /// Schedule the batch and block until it's done.
    /// </summary>
    public void ScheduleAndComplete(int minCommandsPerJob = 32)
    {
        JobHandle handle = CapsulecastCommand.ScheduleBatch(
            commands,
            results,
            minCommandsPerJob,
            maxHits,
            default
        );
        handle.Complete();
    }

    /// <summary>
    /// Get the hits for one query. Returns count and a slice into the big results array.
    /// </summary>
    public int GetHitsForQuery(int queryIndex, out NativeSlice<RaycastHit> hitSlice)
    {
        if (queryIndex < 0 || queryIndex >= nextIndex)
            throw new ArgumentOutOfRangeException(nameof(queryIndex));

        int start = queryIndex * maxHits;
        int count = 0;
        // Unity writes a null‐collider sentinel for “no hit”
        for (int i = 0; i < maxHits; i++)
            if (results[start + i].collider == null)
                break;
            else
                count++;

        hitSlice = results.Slice(start, count);
        return count;
    }

    public void Dispose()
    {
        if (commands.IsCreated) commands.Dispose();
        if (results.IsCreated) results.Dispose();
    }
}