using System;
using Unity.Collections;
using Unity.Jobs;
using UnityEngine;

/// <summary>
/// Batches multiple Box‐cast commands via BoxcastCommand.ScheduleBatch.
/// </summary>
public class BoxCastBatcher : IDisposable
{
    private readonly int maxQueries;
    private readonly int maxHits;
    private NativeArray<BoxcastCommand> commands;
    private NativeArray<RaycastHit> results;
    private int nextIndex;
    
    public int Capacity => maxQueries;

    public BoxCastBatcher(int maxQueries, int maxHits, Allocator allocator = Allocator.TempJob)
    {
        this.maxQueries = maxQueries;
        this.maxHits = maxHits;
        commands = new NativeArray<BoxcastCommand>(maxQueries, allocator);
        results = new NativeArray<RaycastHit>(maxQueries * maxHits, allocator);
        nextIndex = 0;
    }

    public void Reset() => nextIndex = 0;

    /// <summary>
    /// Enqueue one box‐cast query. Returns an index for result lookup.
    /// </summary>
    public int EnqueueQuery(
        Vector3 center,
        Vector3 halfExtents,
        Quaternion orientation,
        Vector3 direction,
        float distance,
        LayerMask layerMask,
        QueryTriggerInteraction hitTriggers = QueryTriggerInteraction.UseGlobal,
        bool hitBackfaces = false,
        bool hitMultipleFaces = false
    )
    {
        if (nextIndex >= maxQueries)
            throw new InvalidOperationException($"Exceeded max box queries ({maxQueries})");

        var qp = QueryParameters.Default;
        qp.layerMask = layerMask;
        qp.hitTriggers = hitTriggers;
        qp.hitBackfaces = hitBackfaces;
        qp.hitMultipleFaces = hitMultipleFaces;

        commands[nextIndex] = new BoxcastCommand(
            center,
            halfExtents,
            orientation,
            direction,
            qp,
            distance
        );

        return nextIndex++;
    }

    public void ScheduleAndComplete(int minCommandsPerJob = 32)
    {
        JobHandle handle = BoxcastCommand.ScheduleBatch(
            commands,
            results,
            minCommandsPerJob,
            maxHits,
            default
        );
        handle.Complete();
    }

    public int GetHitsForQuery(int queryIndex, out NativeSlice<RaycastHit> hitSlice)
    {
        if (queryIndex < 0 || queryIndex >= nextIndex)
            throw new ArgumentOutOfRangeException(nameof(queryIndex));

        int start = queryIndex * maxHits;
        int count = 0;
        for (int i = 0; i < maxHits; i++)
            if (results[start + i].collider == null)
                break;
            else
                count++;

        hitSlice = results.Slice(start, count);
        return count;
    }

    public void Dispose()
    {
        if (commands.IsCreated) commands.Dispose();
        if (results.IsCreated) results.Dispose();
    }
}