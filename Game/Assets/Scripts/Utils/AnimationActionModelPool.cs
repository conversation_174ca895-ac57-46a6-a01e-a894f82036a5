using System.Collections.Generic;
public static class AnimationActionModelPool
{
    private static int maxSize = 10000;
    private static readonly Stack<AnimationActionModel> stack = new Stack<AnimationActionModel>();
    
    public static AnimationActionModel Get(AnimationActionAsset asset)
    {
        var item = stack.Count > 0 ? stack.Pop() : new AnimationActionModel(asset);
        item.Reset(asset);
        return item;
    }

    public static void Release(AnimationActionModel item)
    {
        if (item == null)
        {
            return;
        }
        item.Reset(null);
        if (stack.Count < maxSize)
            stack.Push(item);
    }
}
