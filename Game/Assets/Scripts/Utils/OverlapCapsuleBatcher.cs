using System;
using Unity.Collections;
using Unity.Jobs;
using UnityEngine;

public class OverlapCapsuleBatcher : IDisposable
{
    private readonly int maxQueries;
    private readonly int maxHitsPerQuery;
    private NativeArray<OverlapCapsuleCommand> commands;
    private NativeArray<ColliderHit> results;
    private int nextQueryIndex;
    
    public int Capacity => maxQueries;

    /// <summary>
    /// Create a batcher for up to maxQueries capsule-overlaps,
    /// each storing up to maxHitsPerQuery collider hits.
    /// </summary>
    public OverlapCapsuleBatcher(int maxQueries, int maxHitsPerQuery, Allocator allocator = Allocator.TempJob)
    {
        this.maxQueries = maxQueries;
        this.maxHitsPerQuery = maxHitsPerQuery;
        commands = new NativeArray<OverlapCapsuleCommand>(maxQueries, allocator);
        results = new NativeArray<ColliderHit>(maxQueries * maxHitsPerQuery, allocator);
        nextQueryIndex = 0;
    }

    /// <summary>
    /// Clears out any previously enqueued queries so you can reuse this batcher.
    /// </summary>
    public void Reset()
    {
        nextQueryIndex = 0;
    }

    /// <summary>
    /// Enqueue one capsule overlap test. Returns an index for retrieving hits.
    /// </summary>
    /// <param name="bottom">Start point of the capsule (world space)</param>
    /// <param name="top">End point of the capsule (world space)</param>
    /// <param name="radius">Capsule radius</param>
    /// <param name="layerMask">Which layers to test against</param>
    /// <param name="hitTriggers">Whether to include trigger colliders</param>
    /// <param name="hitBackfaces">Include backfaces? (rarely needed)</param>
    /// <param name="hitMultipleFaces">Report multiple faces per collider?</param>
    public int EnqueueQuery(
        Vector3 bottom,
        Vector3 top,
        float radius,
        LayerMask layerMask,
        QueryTriggerInteraction hitTriggers = QueryTriggerInteraction.UseGlobal,
        bool hitBackfaces = false,
        bool hitMultipleFaces = false
    )
    {
        if (nextQueryIndex >= maxQueries)
            throw new InvalidOperationException($"Cannot enqueue more than {maxQueries} queries");

        var qp = QueryParameters.Default;
        qp.layerMask = layerMask;
        qp.hitTriggers = hitTriggers;
        qp.hitBackfaces = hitBackfaces;
        qp.hitMultipleFaces = hitMultipleFaces;

        commands[nextQueryIndex] = new OverlapCapsuleCommand(bottom, top, radius, qp);
        return nextQueryIndex++;
    }

    /// <summary>
    /// Schedule all enqueued capsule-overlaps in parallel
    /// and block until they’re done.
    /// </summary>
    public void ScheduleAndComplete(int minCommandsPerJob = 32)
    {
        JobHandle handle = OverlapCapsuleCommand.ScheduleBatch(
            commands,
            results,
            minCommandsPerJob,
            maxHitsPerQuery,
            default
        );
        handle.Complete();
    }

    /// <summary>
    /// Pull out the hits for queryIndex. Returns the hit count
    /// and outputs a NativeArray you must Dispose().
    /// </summary>
    public int GetHitsForQuery(int queryIndex, out NativeSlice<ColliderHit> hitSlice)
    {
        if (queryIndex < 0 || queryIndex >= nextQueryIndex)
            throw new ArgumentOutOfRangeException(nameof(queryIndex));

        int start = queryIndex * maxHitsPerQuery;
        int count = 0;
        // Unity writes a null‐collider sentinel when there are no more hits
        for (int i = 0; i < maxHitsPerQuery; i++)
        {
            if (results[start + i].collider == null)
                break;
            count++;
        }

        // Create a slice of length=count starting at “start”
        hitSlice = results.Slice(start, count);
        return count;
    }

    public void Dispose()
    {
        if (commands.IsCreated) commands.Dispose();
        if (results.IsCreated) results.Dispose();
    }
}