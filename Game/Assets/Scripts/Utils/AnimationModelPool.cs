using System.Collections.Generic;
public static class AnimationModelPool
{
    private static int maxSize = 10000;
    private static readonly Stack<AnimationModel> stack = new Stack<AnimationModel>();
    
    public static AnimationModel Get(AnimationAsset asset)
    {
        var item = stack.Count > 0 ? stack.Pop() : new AnimationModel(asset);
        item.Reset(asset);
        return item;
    }

    public static void Release(AnimationModel item)
    {
        if (item == null)
        {
            return;
        }
        item.Reset(null);
        if (stack.Count < maxSize)
            stack.Push(item);
    }
}
