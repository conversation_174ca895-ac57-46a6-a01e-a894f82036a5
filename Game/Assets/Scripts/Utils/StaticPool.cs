using System;
using System.Collections.Generic;

public static class StaticPool<TModel>
    where TModel : class
{
    private static int maxSize = 10000;
    private static readonly Stack<TModel> _stack = new Stack<TModel>();

    public static TModel Get(Func<TModel> factory, Action<TModel> reset = null)
    {
        var item = _stack.Count > 0 ? _stack.Pop() : factory();
        reset?.Invoke(item);
        return item;
    }

    public static void Release(TModel item, Action<TModel> reset = null)
    {
        if (item == null)
        {
            return;
        }
        reset?.Invoke(item);
        if (_stack.Count < maxSize)
            _stack.Push(item);
    }
}
