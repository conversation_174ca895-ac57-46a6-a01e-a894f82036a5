using System.Collections.Generic;

public class LRUCache<TKey, TValue>
{
    private readonly int capacity;
    private readonly Dictionary<TKey, LinkedListNode<CacheItem>> map;
    private readonly LinkedList<CacheItem> list;

    private struct CacheItem
    {
        public TKey Key;
        public TValue Value;
        public CacheItem(TKey key, TValue value)
        {
            Key = key;
            Value = value;
        }
    }

    public LRUCache(int capacity)
    {
        this.capacity = capacity;
        map  = new Dictionary<TKey, LinkedListNode<CacheItem>>(capacity);
        list = new LinkedList<CacheItem>();
    }

    public bool TryGet(TKey key, out TValue value)
    {
        if (map.TryGetValue(key, out var node))
        {
            list.Remove(node);
            list.AddFirst(node);

            value = node.Value.Value;
            return true;
        }

        value = default;
        return false;
    }

    public void Add(TKey key, TValue value)
    {
        if (map.TryGetValue(key, out var existingNode))
        {
            list.Remove(existingNode);
            map.Remove(key);
        }
        else if (map.Count >= capacity)
        {
            var lru = list.Last;
            map.Remove(lru.Value.Key);
            list.RemoveLast();
        }

        var newItem = new CacheItem(key, value);
        var newNode = new LinkedListNode<CacheItem>(newItem);
        list.AddFirst(newNode);
        map[key] = newNode;
    }
    
    public void Clear()
    {
        map.Clear();
        list.Clear();
    }
}
