using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class Constants
{
    public static float TriggerSensitivity = 0.1f;

    public const string AttackHold = "AttackHold";
    public const string AttackHoldRelease = "AttackHoldRelease";
    public const string SoftFinishEvent = "SoftFinish";
    public const string SoftFinishActionEvent = "SoftFinishAction";
    public const string ComboEvent = "Combo";
    public const string SoftStartEvent = "SoftStart";
    public const string InvulnerableStartEvent = "InvStart";
    public const string InvulnerableEndEvent = "InvEnd";
}
