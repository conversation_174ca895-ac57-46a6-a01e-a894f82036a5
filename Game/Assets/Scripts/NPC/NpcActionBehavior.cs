using System.Collections.Generic;
using UnityEngine;

public class NpcActionBehavior : BaseMonoBehavior
{
    public List<TargetActionBehaviorModel> TargetActionModels;

    private Combatant combatant;
    private WeightedRandomSelector<ActionBehaviorModel> randomSelector;
    
    private float waitTimer;
    private bool waitingForAction;
    private bool actionCompleted;
    private const float WaitInterval = 0.1f;

    void Start()
    {
        combatant = GetComponent<Combatant>();
        List<WeightedRandomSelector<ActionBehaviorModel>.Item> items = new(TargetActionModels.Count);
        foreach (var model in TargetActionModels)
        {
            items.Add(model.ToWeightedItem());
        }
        randomSelector = new WeightedRandomSelector<ActionBehaviorModel>(items);
    }
    
    void Update()
    {
        if (combatant.TargetProvider.CurrentTarget == null)
        {
            return;
        }
        
        if (waitTimer > 0f)
        {
            waitTimer -= Time.deltaTime;
            return;
        }

        if (!enabled) return;

        if (waitingForAction)
        {
            if (!actionCompleted)
                return;

            waitingForAction = false;
            waitTimer = WaitInterval;
            return;
        }

        actionCompleted = false;
        var actionModel = randomSelector.Next();
        actionModel.Execute(combatant, OnExecuteComplete);
        waitingForAction = true;
    }
    
    private void OnExecuteComplete(bool success)
    {
        actionCompleted = true;
    }
}
