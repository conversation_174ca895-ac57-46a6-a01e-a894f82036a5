using System;
using UnityEngine;

public class MovementBehavior : BaseMonoBehavior
{
    private Combatant combatant;
    private Vector3 movementDirection;
    private EntityMovementController movementController;
    
    private void Start()
    {
        combatant = GetComponent<Combatant>();
        movementController = GetComponent<EntityMovementController>();
    }

    private void Update()
    {
        if (combatant.IsDead)
        {
            StopMovement();
            return;
        }
        UpdateMovement();
        UpdateAnimation();
    }

    public bool IsGrounded => movementController == null || movementController.IsGrounded;

    public void Move(Vector3 direction)
    {
        movementDirection = direction;
    }

    public void StopVelocity()
    {
        movementController.StopVelocity();
    }

    public void Launch(Vector3 force, bool cancelExistingVerticalVelocity = false)
    {
        movementController.Launch(force, cancelExistingVerticalVelocity);
    }

    public void FastRotate(Vector3 direction)
    {
        movementController.FastRotate(direction);
    }
    
    public void FastRotate(Transform target)
    {
        var direction = VectorHelpers.GetDirectionXZ(transform, target.transform);
        FastRotate(direction);
    }

    public void FastRotate()
    {
        movementController.FastRotate(movementDirection);
    }

    public void SetLookAtTarget(GameObject target)
    {
        movementController.LookAtTarget = target;
    }

    public void SetToNonInteractive()
    {
        movementController.SetToNonInteractive();
    }

    private void UpdateMovement()
    {
        if (!enabled)
        {
            return;
        }
        
        if (combatant.ActionManager.PlayingMovementDisablingAction)
        {
            StopMovement();
            return;
        }
        else
        {
            movementController.EnableMovement();
            movementController.EnableRotation();
            movementController.StopFastRotate();
        }
        
        movementController.SetMovementDirection(movementDirection);
    }
    
    private void UpdateAnimation()
    {
        if (!enabled)
        {
            return;
        }

        if (combatant.ActionManager.PlayingMovementDisablingAction)
        {
            return;
        }

        var model = combatant.EquipmentManager.Weapon.Animation.RunMovement.GetModel(movementDirection, IsGrounded,
            movementController.Rotation, movementController.Velocity);
        var currentAction = combatant.ActionManager.Current;
        if (currentAction == null || currentAction.ActionAsset != model)
        {
            combatant.ActionManager.Execute(model);
        }
    }

    private void StopMovement()
    {
        movementController.DisableMovement();
        movementController.DisableRotation();
        movementController.SetMovementDirection(Vector3.zero);
    }
}
