using Unity.Burst;
using Unity.Burst.Intrinsics;
using Unity.Collections;
using Unity.Entities;
using Unity.Mathematics;
using Unity.Physics;
using Unity.Physics.Systems;
using Unity.Transforms;

[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateAfter(typeof(PhysicsSystemGroup))]
[BurstCompile]
public partial struct BoidSeparationSystem : ISystem
{
    private const float SeparationRadius = 1.5f;
    private const float SeparationWeight = 0.5f;

    public void OnCreate(ref SystemState state)
    {
        state.RequireForUpdate<PhysicsWorldSingleton>();
    }

    [BurstCompile]
    public void OnUpdate(ref SystemState state)
    {
        var physicsWorld = SystemAPI.GetSingleton<PhysicsWorldSingleton>().PhysicsWorld;
        var entityType = state.GetEntityTypeHandle();
        var localTransformType = state.GetComponentTypeHandle<LocalTransform>(true);
        var colliderType = state.GetComponentTypeHandle<PhysicsCollider>(true);
        var separationDataHandle = state.GetComponentTypeHandle<BoidSeparationComponent>(false);
        var boidLookup = state.GetComponentLookup<BoidTag>(true);
        var boidQuery = SystemAPI.QueryBuilder()
            .WithAll<LocalTransform, PhysicsCollider, BoidSeparationComponent, BoidTag>()
            .Build();

        var job = new BoidSeparationJob
        {
            LocalTransformType = localTransformType,
            ColliderType = colliderType,
            SeparationDataType = separationDataHandle,
            EntityType = entityType,
            BoidLookup = boidLookup,
            PhysicsWorld = physicsWorld
        };

        state.Dependency = job.ScheduleParallel(boidQuery, state.Dependency);
    }

    [BurstCompile]
    private struct BoidSeparationJob : IJobChunk
    {
        [ReadOnly] public ComponentTypeHandle<LocalTransform> LocalTransformType;
        [ReadOnly] public ComponentTypeHandle<PhysicsCollider> ColliderType;
        public ComponentTypeHandle<BoidSeparationComponent> SeparationDataType;
        [ReadOnly] public EntityTypeHandle EntityType;
        [ReadOnly] public ComponentLookup<BoidTag> BoidLookup;
        [ReadOnly] public PhysicsWorld PhysicsWorld;

        public void Execute(in ArchetypeChunk chunk, int unfilteredChunkIndex, bool useEnabledMask, in v128 chunkEnabledMask)
        {
            var localTransforms = chunk.GetNativeArray(ref LocalTransformType);
            var colliders = chunk.GetNativeArray(ref ColliderType);
            var entities = chunk.GetNativeArray(EntityType);
            var separationData = chunk.GetNativeArray(ref SeparationDataType);
            var hits = new NativeList<DistanceHit>(16, Allocator.Temp);

            for (int i = 0; i < chunk.Count; i++)
            {
                float3 currentPosition = localTransforms[i].Position;
                float3 separation = float3.zero;
                int neighborCount = 0;

                var input = new PointDistanceInput
                {
                    Position = currentPosition,
                    MaxDistance = SeparationRadius,
                    Filter = colliders[i].Value.Value.GetCollisionFilter()
                };

                hits.Clear();
                PhysicsWorld.CalculateDistance(input, ref hits);

                for (int j = 0; j < hits.Length; j++)
                {
                    var hit = hits[j];
                    Entity otherEntity = PhysicsWorld.Bodies[hit.RigidBodyIndex].Entity;

                    // Skip self or entities without the BoidTag
                    if (otherEntity == entities[i] || !BoidLookup.HasComponent(otherEntity))
                        continue;

                    float3 away = currentPosition - hit.Position;
                    away.y = 0f;
                    float dist = math.length(away);

                    if (dist > 0.01f)
                    {
                        separation += math.normalize(away) / dist;
                        neighborCount++;
                    }
                }

                if (neighborCount > 0)
                    separation /= neighborCount;

                separationData[i] = new BoidSeparationComponent
                {
                    SeparationVector = separation * SeparationWeight
                };
            }

            hits.Dispose();
        }
    }
}
