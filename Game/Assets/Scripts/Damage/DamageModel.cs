using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class DamageModel
{
    public float DamageMin = 15f;
    public float DamageMax = 25f;
    public float MagicDamageMin = 0f;
    public float MagicDamageMax = 0f;
    public Vector3 Force = Vector3.zero;
    public bool Ragdoll = false;
    public Combatant Originator = null;
    public bool Parryable = false;
    public bool Blockable = true;
    public bool Dodgeable = true;
    public bool Flinchable = true;
    public bool ShouldDamage = true;
    public bool Devastating = false;
    public bool ShouldParry = false;
    public bool ShouldBlock = false;
    public bool ShouldFlinch = false;
    public bool SyncDeath = false;
    public bool DeathArea = false;
    public float BurnDamage = 0f;
    public float BurnChance = 0f;
    public float LightningDamage = 0f;
    public float LightningChance = 0f;
    public float VoidInflictChance = 0f;
    public float VoidInflictDamage = 0f;
    public bool Critical = false;
    public bool IgnoreArmor = false;
    public float FlinchModifier = 1f;
    public float CriticalDamageModifier = 1f;
    public bool IsProjectile = false;
    public bool Blocked = false;

    private float ActualDamage;
    private float ActualMagicDamage;

    public DamageModel(float damageMin, float damageMax, float magicalDamageMin = 0f, float magicalDamageMax = 0f)
    {
        this.DamageMin = damageMin;
        this.DamageMax = damageMax;
        this.MagicDamageMin = magicalDamageMin;
        this.MagicDamageMax = magicalDamageMax;

        ActualDamage = Random.Range(DamageMin, DamageMax);
        ActualMagicDamage = Random.Range(MagicDamageMin, MagicDamageMax);
    }

    public float GetDamage()
    {
        return ActualDamage;
    }

    public float GetMagicDamage()
    {
        return ActualMagicDamage;
    }

    public float GetTotalDamage()
    {
        return GetDamage() + GetMagicDamage();
    }

    public void MakeCritical()
    {
        if (Critical)
        {
            return;
        }

        Critical = true;
        ActualDamage *= CriticalDamageModifier;
        ActualMagicDamage *= CriticalDamageModifier;
    }

    public void UpdateDamage(HealthManager healthManager)
    {
        if (healthManager != null && Devastating)
        {
            var currentHealth = healthManager.GetCurrentValue();
            if (currentHealth <= 1f)
            {
                ActualDamage = 1f;
            }
            else
            {
                ActualDamage = currentHealth - 1f;
            }
        }
    }

    public void ReducePhysicalDamage(float percent)
    {
        ActualDamage -= (ActualDamage * percent);
    }

    public void ReduceMagicalDamage(float percent)
    {
        ActualMagicDamage -= (ActualMagicDamage * percent);
    }

    public Vector3 GetDirection()
    {
        return Force.normalized;
    }

    public DamageModel Copy()
    {
        var copy = new DamageModel(DamageMin, DamageMax, MagicDamageMin, MagicDamageMax);
        copy.Force = Force;
        copy.Ragdoll = Ragdoll;
        copy.Originator = Originator;
        copy.Blockable = Blockable;
        copy.ShouldDamage = ShouldDamage;
        copy.Parryable = Parryable;
        copy.Devastating = Devastating;
        copy.ActualDamage = ActualDamage;
        copy.ActualMagicDamage = ActualMagicDamage;
        copy.Dodgeable = Dodgeable;
        copy.ShouldParry = ShouldParry;
        copy.ShouldBlock = ShouldBlock;
        copy.ShouldFlinch = ShouldFlinch;
        copy.SyncDeath = SyncDeath;
        copy.DeathArea = DeathArea;
        copy.BurnDamage = BurnDamage;
        copy.BurnChance = BurnChance;
        copy.LightningDamage = LightningDamage;
        copy.LightningChance = LightningChance;
        copy.VoidInflictChance = VoidInflictChance;
        copy.VoidInflictDamage = VoidInflictDamage;
        copy.Critical = Critical;
        copy.IgnoreArmor = IgnoreArmor;
        copy.FlinchModifier = FlinchModifier;
        copy.CriticalDamageModifier = CriticalDamageModifier;
        copy.Flinchable = Flinchable;
        copy.IsProjectile = IsProjectile;

        return copy;
    }
}
