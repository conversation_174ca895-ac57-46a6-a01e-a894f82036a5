using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UniRx;

public class DamageManager : BaseMonoBehavior
{
    public float FlinchChance = 1f;
    private Combatant combatant;
    private BlockBehavior blockBehavior;

    private Subject<DamageModel> OnHurtSubject = new Subject<DamageModel>();
    public System.IObservable<DamageModel> OnHurtObservable => OnHurtSubject;

    void Start()
    {
        combatant = GetComponent<Combatant>();
        blockBehavior = GetComponent<BlockBehavior>();
    }

    public DamageResult Damage(DamageModel damageModel)
    {
        if (combatant.IsDead || combatant.IsInvulnerable)
        {
            return new DamageResult(false);
        }

        if (combatant.ActionManager.CurrentActionIsBlocking())
        {
            if (damageModel.Blockable)
            {
                damageModel.Blocked = true;
            }
        }

        OnHurtSubject.OnNext(damageModel);
        return HandleHit(damageModel);
    }

    private DamageResult HandleHit(DamageModel damageModel)
    {
        combatant.MovementBehavior.Launch(damageModel.Force);
        HandleFlinch(damageModel);
        var result = new DamageResult(true);
        result.Parried = HandleParry(damageModel);
        return result;
    }

    private void HandleFlinch(DamageModel damageModel)
    {
        if (damageModel.Blocked)
        {
            return;
        }

        combatant.AnimationManager.TriggerHitStopHurt();
        if (combatant.AnimationManager.Current?.Holding == true)
        {
            return;
        }

        if (combatant.ActionManager.Current?.Power == true)
        {
            return;
        }

        if (Random.value < FlinchChance)
        {
            var hurtAction = combatant.Weapon.Animation.Hit;
            combatant.ActionManager.Execute(hurtAction);
        }
    }

    private bool HandleParry(DamageModel damageModel)
    {
        if (!damageModel.Blocked)
        {
            return false;
        }

        if (blockBehavior != null && blockBehavior.InParryWindow())
        {
            return true;
        }

        return false;
    }
}
