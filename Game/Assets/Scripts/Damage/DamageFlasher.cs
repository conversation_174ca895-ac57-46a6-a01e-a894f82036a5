using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UniRx;

[RequireComponent(typeof(DamageManager))]
public class DamageFlasher : BaseMonoBehavior
{
    private Color flashEmissionColor = new Color(1f, 1f, 1f, 0f);
    private Color defaultEmissionColor = new Color(0f, 0f, 0f, 0f);
    private float flashDuration = 0.2f;

    private static readonly int EmissionColorPropertyId = Shader.PropertyToID("_EmissionColor");
    private List<Renderer> rendererList;
    private MaterialPropertyBlock[] propertyBlocks;
    private DamageManager damageManager;
    private Coroutine flashCoroutine;

    private void Awake()
    {
        rendererList = GetComponentsInChildren<Renderer>().ToList();
        propertyBlocks = new MaterialPropertyBlock[rendererList.Count];

        for (int i = 0; i < rendererList.Count; i++)
        {
            var rend = rendererList[i];
            propertyBlocks[i] = new MaterialPropertyBlock();

            foreach (var mat in rend.sharedMaterials)
            {
                if (mat != null && mat.HasProperty(EmissionColorPropertyId))
                {
                    mat.EnableKeyword("_EMISSION");
                    mat.globalIlluminationFlags = MaterialGlobalIlluminationFlags.RealtimeEmissive;
                    mat.SetColor(EmissionColorPropertyId, defaultEmissionColor);
                }
            }
        }
    }

    private void Start()
    {
        damageManager = GetComponent<DamageManager>();

        AddDisposable(damageManager
            .OnHurtObservable
            .Where(hit => !hit.Blocked)
            .Subscribe(_ => TriggerFlash(), Debug.LogError)
        );
    }

    private void OnDisable()
    {
        if (flashCoroutine != null) StopCoroutine(flashCoroutine);
        RestoreEmission();
    }

    private void TriggerFlash()
    {
        if (flashCoroutine != null)
        {
            StopCoroutine(flashCoroutine);
            RestoreEmission();
        }

        flashCoroutine = StartCoroutine(FlashRoutine());
    }

    private IEnumerator FlashRoutine()
    {
        SetEmission(flashEmissionColor);
        yield return new WaitForSeconds(flashDuration);
        RestoreEmission();
        flashCoroutine = null;
    }

    private void SetEmission(Color color)
    {
        for (int i = 0; i < rendererList.Count; i++)
        {
            var rend = rendererList[i];
            var block = propertyBlocks[i];

            block.Clear();
            block.SetColor(EmissionColorPropertyId, color);
            rend.SetPropertyBlock(block);
        }
    }

    private void RestoreEmission()
    {
        SetEmission(defaultEmissionColor);
    }
}